<link rel="stylesheet" href="{{ 'component-list-menu.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-search.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-menu-drawer.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-cart-notification.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-cart-items.css' | asset_url }}" media="print" onload="this.media='all'">
{%- if settings.predictive_search_enabled -%}
  <link rel="stylesheet" href="{{ 'component-price.css' | asset_url }}" media="print" onload="this.media='all'">
  <link rel="stylesheet" href="{{ 'component-loading-overlay.css' | asset_url }}" media="print" onload="this.media='all'">
{%- endif -%}

  <link rel="stylesheet" href="{{ 'component-mega-menu.css' | asset_url }}" media="print" onload="this.media='all'">
  <noscript>{{ 'component-mega-menu.css' | asset_url | stylesheet_tag }}</noscript>

{%- if settings.cart_type == "drawer" -%}
  {{ 'component-cart-drawer.css' | asset_url | stylesheet_tag }}
  {{ 'component-cart.css' | asset_url | stylesheet_tag }}
  {{ 'component-totals.css' | asset_url | stylesheet_tag }}
  {{ 'component-price.css' | asset_url | stylesheet_tag }}
  {{ 'component-discounts.css' | asset_url | stylesheet_tag }}
  {{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
{%- endif -%}



<noscript>{{ 'component-list-menu.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-search.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-menu-drawer.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-cart-notification.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-cart-items.css' | asset_url | stylesheet_tag }}</noscript>
 
<style> 
  
.megamenu_megamenu.mega-menu {
    position: static;
}
.megamenu_megamenu{
  position:relative;
}  
.count-zero {
    display: none;
}
  .count-exist.count-zero {display:block;}
  .header__icons #dT_TopStickySearchBtn.icon-search,
  .search-icon  #dT_TopStickySearchBtn.icon-search{cursor:pointer;width: 18px; height: 18px;display: flex;}
  
  @media screen and (min-width: {{section.settings.mobile_screen_width }}px) {
    header-drawer {
      display: none;
    }
   .header__icons .icon-search { margin-right: 1rem;}
    .header:not(.header--top-center) * > .header__search,
    .header--top-center > .header__search {
    display: inline-flex;
    }
    .header:not(.header--top-center) > .header__icons .header__search,
    .header--top-center * > .header__icons .header__search,
    .header--middle.secondary-menu-enabled .header__icons .header__search{
    display: none;
    }
    .header__inline-menu {
    display: inline-flex;
    }
    .tabs-nav.mobileTabs {display: none;}
    .header--top-center .header__heading-link, .header--top-center .header__heading {
    justify-self: center;
    text-align: center;
    }
    header .header__icons .header__search.search-box-hide{display:none;}
     .header {
    padding-top: 1.3rem;
    padding-bottom: 1.3rem;
  }
  .header--top-left .header-row,
  .header--middle-left:not(.header--has-menu) .header-row {
    grid-template-areas:
     "heading icons"
     "navigation navigation";
    grid-template-columns: 1fr auto;
  }

  .header--middle-left .header-row {
    grid-template-areas: "heading navigation icons";
   grid-template-columns: 1fr auto 1fr; 
    column-gap: 1rem;
  }
  .header--middle .header-row {
    grid-template-areas: "navigation heading  icons";
    grid-template-columns: 1fr auto  1fr;
    column-gap: 1rem;
  }  
  .header--middle.secondary-menu-enabled .header-row {
    grid-template-areas: "left-icon navigation heading secondary-menu icons";
    grid-template-columns: 1fr 1.5fr auto 1.5fr 1fr;
    column-gap: 1rem;
  }  
  .header--middle.secondary-menu-enabled .header-row #AccessibleNav {
    justify-content: center;
  }
  .header--middle.secondary-menu-enabled .header-row #AccessibleNav ul.dt-nav > li > a {padding:0px 20px;line-height: normal;}
  .header--middle.secondary-menu-enabled .header-row #AccessibleNav ul.dt-nav > li.top-level-link{padding:0;}  
  .header--top-center .header-row{
    grid-template-areas:
      "left-icon heading icons "
      "navigation navigation navigation";
    column-gap: 1.5rem;
    row-gap:1.5rem;
    grid-template-columns: 1fr auto 1fr;
  }
   .header--top-left .header-row{
    grid-template-areas:
      "heading left-icon  icons "
      "navigation navigation navigation";
    grid-template-columns: 1fr 1fr 1fr; 
    column-gap: 1.5rem;
    row-gap:1.5rem; 
  }
  .category-menu-button.header--top-left .header-row{
    grid-template-areas:
      "heading left-icon  icons "
      "category-menu navigation navigation";
    grid-template-columns:auto 1fr auto; 
    column-gap: 1.5rem;
    row-gap:1.5rem; 
  }
  .category-menu-button.header--top-center .header-row{
    grid-template-areas:
      "left-icon heading icons "
      "category-menu navigation navigation";
      grid-template-columns:auto 1fr auto; 
      column-gap: 1.5rem;
    row-gap:1.5rem;
  }
  .header--top-center .header__heading-link, .header--top-center .header__heading {
    justify-self: center;
    text-align: center;
}
 {%  if section.settings.logo_position == 'top-center' %}
    .header__icons #dT_TopStickySearchBtn.icon-search{display:none;} 
 {% endif %}  
 .header .search-modal__form{margin-right:0rem;}
 .header .search-modal__form .icon-search {margin: 0;}
 ul.dt-sc-list-inline>li ul.sub-menu-lists>li>ul a:hover{transform:translateX(5px);-webkit-transform:translateX(5px);}   .header__icon--menu .icon {
  display: block;
  position: absolute;
  opacity: 1;
  transform: scale(1);
  transition: transform 150ms ease, opacity 150ms ease;
  width:2rem;height:2rem;
  top: 5px;
}
  }
@media screen and (max-width: {{section.settings.mobile_screen_width  | minus: 1}}px) {
  .header__icon--menu .icon {
  display: block;
  position: absolute;
  opacity: 1;
  transform: scale(1);
  transition: transform 150ms ease, opacity 150ms ease;
  width:2rem;height:2rem;
  top: 5px;
}
        .header:not(.header--top-center) >  .header__search,
        .header--top-center * >  .header__search,
        .header--middle.secondary-menu-enabled  .header__search
        {
         display: none;
        }
        .header:not(.header--top-center) > .header__icons .header__search,
        .header--top-center * > .header__icons .header__search,
        .header--middle.secondary-menu-enabled .header__icons .header__search{
        display:  inline-flex;
        }
        .page-width.mega{display:none;}
        ul.sub-menu-lists.dt-sc-column.four-column { grid-template-columns: repeat(1,1fr);row-gap: 15px;}
        ul.dt-sc-list-inline > li ul.sub-menu-lists .hide-mobile { display: none !important;}
        ul.dt-sc-list-inline > li ul.sub-menu-lists .tabs-nav .tabs .heading ~ ul{ border: none; display: inline-block; }
        ul.dt-sc-list-inline > li ul.sub-menu-lists .tabs-nav .tabs .heading ~ ul li > a{ border: none; padding: 0 15px !important;}
        ul.dt-sc-list-inline > li ul.sub-menu-lists .tabs-nav .tabs li a { background:transparent; position:relative; margin:0;cursor: pointer; padding:0; }
       ul.dt-sc-list-inline > li ul.sub-menu-lists .tabs-nav .tabs li a:hover {color:rgba(var(--color-base-outline-button-labels));background:transparent; }
        ul.dt-sc-list-inline > li ul.sub-menu-lists .tabs-nav .tabs > li > a:after{ content: ''; position: absolute; right: 15px; top: 50%; width: 6px; height: 6px; border-left: 1px solid currentColor; border-bottom: 1px solid currentColor; transform: rotate(-45deg) translateY(-50%);}
         header .tag.hot, header .tag.sale, header .tag.new{position:relative;left:10px;} 
        header ul.dt-sc-list-inline>li ul.sub-menu-lists>li>ul a .tag{top:0;}
        header .search-box.search-box-hide{display:none;}
        a.header__icon.header__icon--account.link.focus-inset.small-hide,
         header .header__icons   localization-form{display:none}
        a.header__icon:not(:last-child) {margin-left: 2rem;}
    a.header__icon:not(:last-child),
   .header__search{margin-right:0!important;}
   {%  if section.settings.logo_position == 'top-center' %}
   .search-icon  #dT_TopStickySearchBtn.icon-search{display:none;}
   {% endif %}
   
  .menu-drawer ul.sub-menu-lists{ display: inline-block;}
  .dt-sc-nav-link.dropdown{display:none;}
  
  .js .menu-drawer__menu li:not(.has-mega-menu) .sub-menu-lists{padding:0;}
  .header--middle-left .header-row{
    /* grid-template-areas:
          "left-icon icons"
          "heading heading "; */
      grid-template-columns: 1fr 1fr; gap: 2rem;
  }
  .header--middle-left .header-row header-drawer{justify-content: flex-start;}
  .header--middle-left .header-row .header__heading{justify-self: center;}
}
@media screen and (max-width: {{section.settings.mobile_screen_width  | minus: 1}}px)  and (min-width:750px){
        
        .header header-drawer{display: flex;justify-content: flex-end;}
       .category-menu-button.header--top-left .header-row,
       .category-menu-button.header--top-center .header-row{
       grid-template-areas:
      "heading left-icon  icons"
      "category-menu category-menu category-menu";
  }
    .header{padding:20px 0;}

}
@media screen and (max-width:989px){
  .header .header-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: center;
  }
}
@media screen and (max-width:749px){
   .category-menu-button.header--top-left .header-row .category-menu,
   .category-menu-button.header--top-center .header-row .category-menu{
        grid-column: 1;
  }
 } 
@media screen and (max-width:480px){
  .category-menu-button.header--top-left .header-row,
   .category-menu-button.header--top-center .header-row{display: flex;flex-direction: column;}
   .category-menu-button.header--top-center .header-row header-drawer,
   .category-menu-button.header--top-left .header-row header-drawer{order: 1;}
    a.header__icon:not(:last-child) {margin-left: 1rem;}
}
.header__icons:not(.icon__fallback-content) .header__icon:not(.header__icon--summary) { background: rgba(var(--color-button),1); margin: 0;min-width: 6rem; height: 4.5rem; border-radius: 3rem; margin-right: 0; transition:all 0.3s linear}
.header__icons:not(.icon__fallback-content) .header__icon:not(.header__icon--summary) .cart-icon {display: flex; align-items: center; justify-content: center;}
header svg{width:1.6rem;height:1.6rem;}  
  .menu-drawer-container {
    display: flex;
  }
 
  a.header__icon:not(:last-child),
  .header__search {
    margin-right: 0rem;
}
  .list-menu {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .list-menu--inline {
    display: inline-flex;
    flex-wrap: wrap;
  }

  summary.list-menu__item {
    padding-right: 2.7rem;
  }

  .list-menu__item {
    display: flex;
    align-items: center;
    line-height: calc(1 + 0.3 / var(--font-body-scale));
  }

  .list-menu__item--link {
    text-decoration: none;
    padding-bottom: 1rem;
    padding-top: 1rem;
    line-height: calc(1 + 0.8 / var(--font-body-scale));
  }

  @media screen and (min-width: 750px) {
    .list-menu__item--link {
      padding-bottom: 0.5rem;
      padding-top: 0.5rem;
    }
    
  }
  .header ul.dt-sc-list-inline.dt-desktop-menu.dt-nav {
    z-index: 2;
}
 /* ul.dt-nav > li.top-level-link{display: inline-flex;}
ul.dt-nav > li.top-level-link a.dt-sc-nav-link  {overflow: hidden; display: inline-block;padding:0;border-radius:0;} */
  ul.dt-nav > li.top-level-link a.dt-sc-nav-link span:not(.dt-sc-caret){position:relative;display: inline-flex; /* -webkit-transition:all .4s cubic-bezier(0.68, -0.55, 0.265, 1.55); -o-transition:all .4s cubic-bezier(0.68, -0.55, 0.265, 1.55); transition:all .4s cubic-bezier(0.68, -0.55, 0.265, 1.55);*/}
/* ul.dt-nav.dt-desktop-menu > li.top-level-link a.dt-sc-nav-link span:not(.dt-sc-caret):after{content:attr( data-hover ); display:block; width:100%; height:100%; position:absolute; left:0; top:0; text-align:center;
	-webkit-transform:translateY( -100% );
	-ms-transform:translateY( -100% );
	-o-transform:translateY( -100% );
	transform:translateY( -100% );
}
 ul.dt-nav.dt-desktop-menu > li.top-level-link a.dt-sc-nav-link:hover span:not(.dt-sc-caret){
	-webkit-transform:translateY( 100% );
	-ms-transform:translateY( 100% );
	-o-transform:translateY( 100% );
	transform:translateY( 100% );
}  */

.dt-sc-nav-link.dropdown{padding:1.4rem; position:relative;}
.header ul.dt-sc-list-inline.dt-desktop-menu.dt-nav{
display: inline-flex;
    flex-wrap: wrap;
    list-style:none;
}

@media screen and (max-width:350px){
  .header .header-row .header__heading{ flex-grow:1; }
  .header .header-row{ justify-content:flex-start; }
  .header .header-row .header__heading a, .header .header-row .header__heading a img { width:100%; }
}
  

</style>

{%- style -%}
  .section-header {
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
  }
  @media screen and (min-width: 750px) {
    .section-header {
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
#dT_top-sticky.search-show {display:flex;}
  
    {%- if section.settings.enable_transparent_header -%}
    .index-header{background: transparent;}
    {% endif %}
{% for block in section.blocks %}
  {% if block.settings.enable_custom_width  %}  
    header nav#AccessibleNav.custom_width_dropdown ul.dt-desktop-menu.dt-nav > li.has-mega-menu > .megamenu_megamenu > div.sub-menu-block.block-{{ block.id }}-type  { 
    width: {{ block.settings.custom_megamenu_width }}%;
    max-width: {{ block.settings.custom_megamenu_width }}%; 
    margin:auto;
  }    
  header nav#AccessibleNav.custom_width_dropdown ul.dt-desktop-menu.dt-nav > li.has-mega-menu > .megamenu_megamenu > div.sub-menu-block.block-{{ block.id }}-type  { 
     box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  background-color: rgb(var(--color-background));
  font-size: 18px;
  }
{% endif %}
 header nav#AccessibleNav.default_dropdown ul.dt-desktop-menu.dt-nav > li.has-mega-menu > .megamenu_megamenu > div.sub-menu-block.block-{{ block.id }}-type  { margin:auto;}
.header nav#AccessibleNav.default_dropdown ul.dt-desktop-menu.dt-nav .megamenu_megamenu ul.sub-menu-lists{padding:0;}
.header ul.dt-sc-list-inline.dt-desktop-menu.dt-nav .megamenu_megamenu .sub-menu-block{    padding: 10px; width:max-content;}  
.header ul.dt-sc-list-inline.dt-desktop-menu.dt-nav .megamenu_megamenu.mega-menu .sub-menu-block{  /*  padding: var(--grid-desktop-horizontal-spacing);*/ width:100%; padding:0;}  
  {% endfor %}
  .cart-count-bubble.count-zero {
    opacity: 0;
        visibility: hidden;
  }
{%- endstyle -%}

 {%  render 'search-overlay-model' %}
{% capture primaryMenu %}
{%- if section.settings.menu != blank -%}
<ul class="dt-sc-list-inline  dt-desktop-menu dt-nav" data-menu="dt-main-menu">     
  {%- for link in linklists[section.settings.menu].links -%}  
  {% render 'navigation', class: 'primaryMenu', link: link %}
  {%- endfor -%}
</ul>
{%- endif -%}
{%  endcapture %}

{% capture secondaryMenu %}
    {%- if section.settings.show_secondary_menu -%}
    {%- if section.settings.secondary_menu != blank -%}
      <ul class="dt-sc-list-inline  dt-desktop-menu dt-nav secondary__nav" data-menu="dt-main-menu">
        {%- for link2 in linklists[section.settings.secondary_menu].links -%}
        {%- assign Fetchlevel = true -%}
        {% render 'navigation', class: 'secondaryMenu', link: link2, pack: 'dt-sc-menu', loop: forloop.index, section: section, Fetchlevel: Fetchlevel, id: 'refular-mega' %} 
        {%- endfor -%}   
      </ul>
    {%- endif -%}
    {%- endif -%}
  {%  endcapture %}

   {% capture categoryMenu %}
    {%- if section.settings.show_category_menu -%}
    {%- if section.settings.category_menu != blank -%}
      <ul class="dt-sc-list-inline  dt-desktop-menu dt-nav category_nav" data-menu="dt-main-menu">
        {%- for link1 in linklists[section.settings.category_menu].links -%}
        {%- assign Fetchlevel = true -%}
        {% render 'category-navigation', class: 'categoryMenu', link: link1, pack: 'dt-sc-menu', loop: forloop.index, section: section, Fetchlevel: Fetchlevel, id: 'refular-mega' %} 
        {%- endfor -%}   
      </ul>
    {%- endif -%}
    {%- endif -%}
  {%  endcapture %}
<script src="{{ 'details-disclosure.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'details-modal.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'cart-notification.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'dt-mega-menu.js' | asset_url }}" defer="defer"></script>
{%- if settings.cart_type == "drawer" -%}
  <script src="{{ 'cart-drawer.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
<script src="{{ 'search-form.js' | asset_url }}" defer="defer"></script>
{% render 'icon-search' %}
<{% if section.settings.enable_sticky_header %}sticky-header{% else %}div{% endif %} id="shopify-section-headers" class="header-wrapper {% if section.settings.show_line_separator %} header-wrapper--border-bottom{% endif %}">
  <header id="header" class=" animate__animated fadeInLeft {% if request.page_type == 'index' %}{%- if section.settings.enable_transparent_header -%}index-header {% endif %}{% endif %} header header--{{ section.settings.logo_position }}  {%- if section.settings.show_secondary_menu -%} secondary-menu-enabled {%- endif -%}    {% if section.settings.menu != blank %} header--has-menu{% endif %} color-{{ section.settings.color_scheme }} {%- if section.settings.show_category_menu -%} category-menu-button {%- endif -%} ">
   <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}">
    <div class="row ">
      <div class="header-row">
    {%- if section.settings.menu != blank -%}
      <header-drawer data-breakpoint="tablet">      
        <details id="Details-menu-drawer-container" class="menu-drawer-container">
          <summary class="header__icon header__icon--menu header__icon--summary link focus-inset" aria-label="{{ 'sections.header.menu' | t }}">
            <span>
              {% render 'icon-hamburger' %}       
            </span>
          </summary>
          <div id="menu-drawer" class="gradient menu-drawer motion-reduce" tabindex="-1">     
          <div class="menu-drawer-header">
          <span class="drawer-title">Menu</span>
          <button class="header-drawer__close close_icon_button"  type="button" aria-label="{{ 'accessibility.close' | t }}">{% render 'icon-close' %}</button>
          </div>
            <div class="menu-drawer__inner-container">
              <div class="menu-drawer__navigation-container">
                 <nav class="menu-drawer__navigation">
                  {%- if section.settings.menu != blank -%}
                    <ul class="menu-drawer__menu has-submenu list-menu dt-sc-list-inline" role="list"> 
                    {%- for link in linklists[section.settings.menu].links -%}  
                    {% render 'mobile-navigation', class: 'primaryMenu', link: link %}
                    {%- endfor -%}
                    {%- if section.settings.show_secondary_menu -%}
                    {%- if section.settings.secondary_menu != blank -%}
                    {%- for link in linklists[section.settings.secondary_menu].links -%}  
                    {% render 'mobile-navigation', class: 'secondaryMenu', link: link %}
                    {%- endfor -%}
                    {%- endif -%}
                    {%- endif -%}
                    </ul>
                    {%- endif -%}
                  </nav>
               
              </div>
            </div>
             <div class="menu-drawer__utility-links">
                  {%- if shop.customer_accounts_enabled -%}
                    <a href="{%- if customer -%}{{ routes.account_url }}{%- else -%}{{ routes.account_login_url }}{%- endif -%}" class="menu-drawer__account link focus-inset h5 small-hide">
                      {% render 'icon-account' %}
                      {%- liquid
                        if customer
                          echo 'customer.account_fallback' | t
                        else
                          echo 'customer.log_in' | t
                        endif
                      -%}
                    </a>
                  {%- endif -%}
                   {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
          <noscript>
            {%- form 'localization', id: 'HeaderCountryFormNoScript', class: 'localization-form' -%}
              <div class="localization-form__select">
                <h2 class="visually-hidden" id="FooterCountryLabelNoScript">{{ 'localization.country_label' | t }}</h2>
                <select class="localization-selector link" name="country_code" aria-labelledby="FooterCountryLabelNoScript">
                  {%- for country in localization.available_countries -%}
                    <option value="{{ country.iso_code }}" {%- if country.iso_code == localization.country.iso_code %} selected{% endif %}>
                      {{ country.name }} ({{ country.currency.iso_code }} {{ country.currency.symbol }})
                    </option>
                  {%- endfor -%}
                </select>
                {% render 'icon-caret' %}
              </div>
              <button class="button button--tertiary">{{ 'localization.update_country' | t }}</button>
            {%- endform -%}
          </noscript>
          <localization-form>
            {%- form 'localization', id: 'HeaderCountryForm', class: 'localization-form' -%}
              <div class="no-js-hidden">
                <h2 class="caption-large text-body visually-hidden" id="FooterCountryLabel">{{ 'localization.country_label' | t }}</h2>
                <div class="disclosure">
                  <button type="button" class="disclosure__button localization-form__select localization-selector link link--text caption-large" aria-expanded="false" aria-controls="FooterCountryList" aria-describedby="FooterCountryLabel">
                    {% assign nameFlagUp = localization.country.iso_code | downcase %}
                    {{ localization.country.iso_code }} <span class="flag-icon flag-icon-{{ nameFlagUp }}"></span>
                    {% render 'icon-caret' %}
                  </button>
                  <div class="disclosure__list-wrapper" hidden>
                    <ul id="FooterCountryList" role="list" class="disclosure__list list-unstyled">
                      {%- for country in localization.available_countries -%}
                        {% assign nameFlag = country.iso_code | downcase %}

                        <li class="disclosure__item" tabindex="-1">
                          <a class="link link--text disclosure__link caption-large{% if country.iso_code == localization.country.iso_code %} disclosure__link--active{% endif %} focus-inset" href="#"{% if country.iso_code == localization.country.iso_code %} aria-current="true"{% endif %} data-value="{{ country.iso_code }}">
                            {{ country.iso_code }} <span class="localization-form__currency flag-icon flag-icon-{{ nameFlag }}"></span>
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                </div>
                <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%}

        {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
          <noscript>
            {%- form 'localization', id: 'HeaderLanguageFormNoScript', class: 'localization-form' -%}
              <div class="localization-form__select">
                <h2 class="visually-hidden" id="FooterLanguageLabelNoScript">{{ 'localization.language_label' | t }}</h2>
                <select class="localization-selector link" name="locale_code" aria-labelledby="FooterLanguageLabelNoScript">
                  {%- for language in localization.available_languages -%}
                    <option value="{{ language.iso_code }}" lang="{{ language.iso_code }}" {%- if language.iso_code == localization.language.iso_code %} selected{% endif %}>
                      {{ language.endonym_name | capitalize }}
                    </option>
                  {%- endfor -%}
                </select>
                {% render 'icon-caret' %}
              </div>
              <button class="button button--tertiary">{{ 'localization.update_language' | t }}</button>
            {%- endform -%}
          </noscript>

          <localization-form>
            {%- form 'localization', id: 'HeaderLanguageForm', class: 'localization-form' -%}
              <div class="no-js-hidden">
                <h2 class="caption-large text-body visually-hidden" id="FooterLanguageLabel">{{ 'localization.language_label' | t }}</h2>
                <div class="disclosure">
                  <button type="button" class="disclosure__button localization-form__select localization-selector link link--text caption-large" aria-expanded="false" aria-controls="FooterLanguageList" aria-describedby="FooterLanguageLabel">
                    {{ localization.language.endonym_name | capitalize }}
                    {% render 'icon-caret' %}
                  </button>
                  <div class="disclosure__list-wrapper" hidden>
                    <ul id="FooterLanguageList" role="list" class="disclosure__list list-unstyled">
                      {%- for language in localization.available_languages -%}
                        <li class="disclosure__item" tabindex="-1">
                          <a class="link link--text disclosure__link caption-large{% if language.iso_code == localization.language.iso_code %} disclosure__link--active{% endif %} focus-inset" href="#" hreflang="{{ language.iso_code }}" lang="{{ language.iso_code }}"{% if language.iso_code == localization.language.iso_code %} aria-current="true"{% endif %} data-value="{{ language.iso_code }}">
                            {{ language.endonym_name | capitalize }}
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                </div>
                <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%} 
                  <ul class="list list-social list-unstyled" role="list">
                    {%- if settings.social_twitter_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_twitter_link }}" class="list-social__link link">
                          {%- render 'icon-twitter' -%}
                          <span class="visually-hidden">{{ 'general.social.links.twitter' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_facebook_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_facebook_link }}" class="list-social__link link">
                          {%- render 'icon-facebook' -%}
                          <span class="visually-hidden">{{ 'general.social.links.facebook' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_pinterest_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_pinterest_link }}" class="list-social__link link">
                          {%- render 'icon-pinterest' -%}
                          <span class="visually-hidden">{{ 'general.social.links.pinterest' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_instagram_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_instagram_link }}" class="list-social__link link">
                          {%- render 'icon-instagram' -%}
                          <span class="visually-hidden">{{ 'general.social.links.instagram' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_tiktok_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_tiktok_link }}" class="list-social__link link">
                          {%- render 'icon-tiktok' -%}
                          <span class="visually-hidden">{{ 'general.social.links.tiktok' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_tumblr_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_tumblr_link }}" class="list-social__link link">
                          {%- render 'icon-tumblr' -%}
                          <span class="visually-hidden">{{ 'general.social.links.tumblr' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_snapchat_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_snapchat_link }}" class="list-social__link link">
                          {%- render 'icon-snapchat' -%}
                          <span class="visually-hidden">{{ 'general.social.links.snapchat' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_youtube_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_youtube_link }}" class="list-social__link link">
                          {%- render 'icon-youtube' -%}
                          <span class="visually-hidden">{{ 'general.social.links.youtube' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                    {%- if settings.social_vimeo_link != blank -%}
                      <li class="list-social__item">
                        <a href="{{ settings.social_vimeo_link }}" class="list-social__link link">
                          {%- render 'icon-vimeo' -%}
                          <span class="visually-hidden">{{ 'general.social.links.vimeo' | t }}</span>
                        </a>
                      </li>
                    {%- endif -%}
                  </ul>
                </div>
          </div>
        </details>
      </header-drawer>
    {%- endif -%}
     
    {%- if section.settings.logo_position == 'top-center' or section.settings.menu == blank or section.settings.logo_position == 'middle' and section.settings.show_secondary_menu -%}
     <div class="search-icon">{%  render 'header-search', section: section %}</div>
    {%- endif -%}    
    {%- if request.page_type == 'index' -%}
      <h1 class="header__heading">
    {%- endif -%}
        <a href="{{ routes.root_url }}" class="header__heading-link link link--text focus-inset">
          {%- if section.settings.logo != blank -%}
            {%- assign image_size_2x = section.settings.logo_width | times: 2 | at_most: 5760 -%}
            <img srcset="{{ section.settings.logo | image_url: width: section.settings.logo_width }} 1x, {{ section.settings.logo | image_url: width: image_size_2x }} 2x"
              src="{{ section.settings.logo | image_url: width: section.settings.logo_width }}"
              loading="lazy"
              class="header__heading-logo"
              width="{{ section.settings.logo_width }}"
              height="{{ section.settings.logo.height }}"
              alt="{{ section.settings.logo.alt | default: shop.name | escape }}"
            >
          {%- else -%}
            <span class="h2">{{ shop.name }}</span>
          {%- endif -%}
        </a>
    {%- if request.page_type == 'index' -%}
      </h1>
    {%- endif -%}

     {%- if section.settings.menu != blank -%}
              <div class="header__inline-menu">
                
                <nav id="AccessibleNav" role="navigation" class="{{ section.settings.dropdown_style }} dt-sc-flex-space-between {{section.settings.menu_type_desktop}} align-{{ section.settings.menu_alignment }}" data-menu="dt-main-menu">
                    {{ primaryMenu }}
                  </nav>
               </div>
      {%- endif -%}
      {%- if section.settings.logo_position == 'middle' -%}
        {%- if section.settings.show_secondary_menu -%}
               <div class="header__inline-menu secondary-menu">
                   <nav id="AccessibleNav" role="secondary-navigation" class="{{ section.settings.dropdown_style }} dt-sc-flex-space-between {{section.settings.menu_type_desktop}} align-{{ section.settings.menu_alignment }}" data-menu="dt-main-menu">
                    {{ secondaryMenu }}
                   </nav>
                </div>     
        {%- endif -%} 
      {%- endif -%}  
      {%- if section.settings.logo_position == 'top-center' or  section.settings.logo_position == 'top-left' -%}
       {%- if section.settings.show_category_menu -%}
         <div class="category-menu">
           <a class="category-heading button" id="category-menu-button">
            <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            viewBox="0 0 50 32" style="enable-background:new 0 0 50 32;" fill="currentcolor" xml:space="preserve">
            <path d="M0,0v2h50V0H0z M0,15v2h50v-2H0z M0,30v2h50v-2H0z" />
            </svg>{{ section.settings.category_heading }}</a>
          <div class="category-wrapper" style="display: none;">
            {%- if section.settings.category_menu != blank  -%}
                    <ul class="category-block__details-content">
                      {%- for link in section.settings.category_menu.links -%}
                        <li>
                          <a href="{{ link.url }}" class="link link--text list-menu__item list-menu__item--link{% if link.active %} list-menu__item--active{% endif %}">
                            {{ link.title }}
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
              {%- endif -%}      
          </div>
        </div>   
      {%- endif -%}   
    {%- endif -%}
                    
    <div class="header__icons {% if section.settings.header_icon_type =="text"  %} icon__fallback-content {% endif %} ">
        {%  unless section.settings.logo_position == 'top-center' and  section.settings.search_style == 'search_box' %}
        {%  render 'header-search' %}      
        {%  endunless %}
              {%- if section.settings.show_header_wishlist -%}        
              <a href="/pages/wishlist" class="header__icon header__icon--wishlist link small-hide">
                {% render 'icon-wishlist' %}
                <span class="icon__fallback-text {% if section.settings.header_icon_type =="icon"  %} hidden {% endif %}">{{ 'products.product.wishlist' | t }}</span>
                <dtx-wish-count class="dtxc-wishlist-count cart-count-bubble" grid_type="wishList" count="0">
                  <div class="grid-count-bubble">
                    <span aria-hidden="true"></span>
                  </div>
                </dtx-wish-count>
              </a>
              {%- endif -%}

              {%- if section.settings.show_header_compare -%}          
              <a href="/pages/compare" class="header__icon header__icon--compare link">
                {% render 'icon-compare' %}
                <span class="icon__fallback-text {% if section.settings.header_icon_type =="icon"  %} hidden {% endif %} ">{{ 'products.product.compare' | t }}</span>
                
                <dtx-compare-count class="dtxc-compare-count cart-count-bubble" grid_type="compareList" count="0">
                  <div class="grid-count-bubble">
                    <span aria-hidden="true"></span>
                  </div>
                  
                </dtx-compare-count>
                  
              </a>          
              {%- endif -%}
   
  {%- if section.settings.show_header_cart -%}   
      <a href="{{ routes.cart_url }}" class="header__icon header__icon--cart link focus-inset" id="cart-icon-bubble">
        {%- liquid
          if cart == empty
            render 'header-cart'
          else
            render 'header-cart'
          endif
        -%}
        <span class="icon__fallback-text {% if section.settings.header_icon_type =="icon"  %} hidden {% endif %}">{{ 'templates.cart.cart' | t }}</span>
        {%- if cart != empty -%}
          <div class="cart-count-bubble">
            {%- if cart.item_count < 100 -%}
              <span aria-hidden="true">{{ cart.item_count }}</span>
            {%- endif -%}
            <span class="visually-hidden">{{ 'sections.header.cart_count' | t: count: cart.item_count }}</span>
          </div>
        {%- endif -%}
      </a>
 {%- endif -%}
         {% comment %}        
     {%- if shop.customer_accounts_enabled and section.settings.show_account -%}
       {%- if customer -%} 
       <a href="{{ routes.account_url }}" class="header__icon header__icon--account link focus-inset{% if section.settings.menu != blank %} small-hide{% endif %}">
        {% else %}
       <a href="#" class="header__icon header__icon--account link focus-inset{% if section.settings.menu != blank %} small-hide{% endif %}">
       {% render 'icon-account' %}
          <span class="icon__fallback-text {% if section.settings.header_icon_type =="icon"  %} hidden {% endif %}">
            {%- liquid
              if customer
                echo 'customer.account_fallback' | t
              else
                echo 'customer.log_in' | t
              endif
            -%}
          </span>
        </a>
         {%- endif -%}
      {%- endif -%}
      {% endcomment %}      
      
        {%- if section.settings.enable_country_selector and localization.available_countries.size > 1 -%}
          <noscript>
            {%- form 'localization', id: 'HeaderCountryFormNoScript', class: 'localization-form' -%}
              <div class="localization-form__select">
                <h2 class="visually-hidden" id="FooterCountryLabelNoScript">{{ 'localization.country_label' | t }}</h2>
                <select class="localization-selector link" name="country_code" aria-labelledby="FooterCountryLabelNoScript">
                  {%- for country in localization.available_countries -%}
                    <option value="{{ country.iso_code }}" {%- if country.iso_code == localization.country.iso_code %} selected{% endif %}>
                      {{ country.name }} ({{ country.currency.iso_code }} {{ country.currency.symbol }})
                    </option>
                  {%- endfor -%}
                </select>
                {% render 'icon-caret' %}
              </div>
              <button class="button button--tertiary">{{ 'localization.update_country' | t }}</button>
            {%- endform -%}
          </noscript>
          <localization-form>
            {%- form 'localization', id: 'HeaderCountryForm', class: 'localization-form' -%}
              <div class="no-js-hidden">
                <h2 class="caption-large text-body visually-hidden" id="FooterCountryLabel">{{ 'localization.country_label' | t }}</h2>
                <div class="disclosure">
                  <button type="button" class="disclosure__button localization-form__select localization-selector link link--text caption-large" aria-expanded="false" aria-controls="FooterCountryList" aria-describedby="FooterCountryLabel">
                    {% assign nameFlagUp = localization.country.iso_code | downcase %}
                    {{ localization.country.iso_code }} <span class="flag-icon flag-icon-{{ nameFlagUp }}"></span>
                    {% render 'icon-caret' %}
                  </button>
                  <div class="disclosure__list-wrapper" hidden>
                    <ul id="FooterCountryList" role="list" class="disclosure__list list-unstyled">
                      {%- for country in localization.available_countries -%}
                        {% assign nameFlag = country.iso_code | downcase %}

                        <li class="disclosure__item" tabindex="-1">
                          <a class="link link--text disclosure__link caption-large{% if country.iso_code == localization.country.iso_code %} disclosure__link--active{% endif %} focus-inset" href="#"{% if country.iso_code == localization.country.iso_code %} aria-current="true"{% endif %} data-value="{{ country.iso_code }}">
                            {{ country.iso_code }} <span class="localization-form__currency flag-icon flag-icon-{{ nameFlag }}"></span>
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                </div>
                <input type="hidden" name="country_code" value="{{ localization.country.iso_code }}">
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%}

        {%- if section.settings.enable_language_selector and localization.available_languages.size > 1 -%}
          <noscript>
            {%- form 'localization', id: 'HeaderLanguageFormNoScript', class: 'localization-form' -%}
              <div class="localization-form__select">
                <h2 class="visually-hidden" id="FooterLanguageLabelNoScript">{{ 'localization.language_label' | t }}</h2>
                <select class="localization-selector link" name="locale_code" aria-labelledby="FooterLanguageLabelNoScript">
                  {%- for language in localization.available_languages -%}
                    <option value="{{ language.iso_code }}" lang="{{ language.iso_code }}" {%- if language.iso_code == localization.language.iso_code %} selected{% endif %}>
                      {{ language.endonym_name | capitalize }}
                    </option>
                  {%- endfor -%}
                </select>
                {% render 'icon-caret' %}
              </div>
              <button class="button button--tertiary">{{ 'localization.update_language' | t }}</button>
            {%- endform -%}
          </noscript>

          <localization-form>
            {%- form 'localization', id: 'HeaderLanguageForm', class: 'localization-form' -%}
              <div class="no-js-hidden">
                <h2 class="caption-large text-body visually-hidden" id="FooterLanguageLabel">{{ 'localization.language_label' | t }}</h2>
                <div class="disclosure">
                  <button type="button" class="disclosure__button localization-form__select localization-selector link link--text caption-large" aria-expanded="false" aria-controls="FooterLanguageList" aria-describedby="FooterLanguageLabel">
                    {{ localization.language.endonym_name | capitalize }}
                    {% render 'icon-caret' %}
                  </button>
                  <div class="disclosure__list-wrapper" hidden>
                    <ul id="FooterLanguageList" role="list" class="disclosure__list list-unstyled">
                      {%- for language in localization.available_languages -%}
                        <li class="disclosure__item" tabindex="-1">
                          <a class="link link--text disclosure__link caption-large{% if language.iso_code == localization.language.iso_code %} disclosure__link--active{% endif %} focus-inset" href="#" hreflang="{{ language.iso_code }}" lang="{{ language.iso_code }}"{% if language.iso_code == localization.language.iso_code %} aria-current="true"{% endif %} data-value="{{ language.iso_code }}">
                            {{ language.endonym_name | capitalize }}
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                </div>
                <input type="hidden" name="locale_code" value="{{ localization.language.iso_code }}">
              </div>
            {%- endform -%}
          </localization-form>
        {%- endif -%}
      </div>  
    </div>
  </div>
 </div>
  </header>
</{% if section.settings.enable_sticky_header %}sticky-header{% else %}div{% endif %}>

{%- if settings.cart_type == "notification" -%}
  {%- render 'cart-notification', color_scheme: section.settings.color_scheme -%}
{%- endif -%}

{% javascript %}
  class StickyHeader extends HTMLElement {
    constructor() {
      super();
    }

    connectedCallback() {
      this.header = document.getElementById('shopify-section-header');
      this.headerBounds = {};
      this.currentScrollTop = 0;
      this.preventReveal = false;
      this.predictiveSearch = this.querySelector('predictive-search');

      this.onScrollHandler = this.onScroll.bind(this);
      this.hideHeaderOnScrollUp = () => this.preventReveal = true;

      this.addEventListener('preventHeaderReveal', this.hideHeaderOnScrollUp);
      window.addEventListener('scroll', this.onScrollHandler, false);

      this.createObserver();
    }

    disconnectedCallback() {
      this.removeEventListener('preventHeaderReveal', this.hideHeaderOnScrollUp);
      window.removeEventListener('scroll', this.onScrollHandler);
    }

    createObserver() {
      let observer = new IntersectionObserver((entries, observer) => {
        this.headerBounds = entries[0].intersectionRect;
        observer.disconnect();
      });

      observer.observe(this.header);
    }

    onScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      if (this.predictiveSearch && this.predictiveSearch.isOpen) return;

      if (scrollTop > this.currentScrollTop && scrollTop > this.headerBounds.bottom) {
        if (this.preventHide) return;
        requestAnimationFrame(this.hide.bind(this));
      } else if (scrollTop < this.currentScrollTop && scrollTop > this.headerBounds.bottom) {
        if (!this.preventReveal) {
          requestAnimationFrame(this.reveal.bind(this));
        } else {
          window.clearTimeout(this.isScrolling);

          this.isScrolling = setTimeout(() => {
            this.preventReveal = false;
          }, 66);

          requestAnimationFrame(this.hide.bind(this));
        }
      } else if (scrollTop <= this.headerBounds.top) {
        requestAnimationFrame(this.reset.bind(this));
      }

      this.currentScrollTop = scrollTop;
    }

    hide() {
      this.header.classList.add('shopify-section-header-hidden', 'shopify-section-header-sticky');
      this.closeMenuDisclosure();
      // this.closeSearchModal();
    }

    reveal() {
      this.header.classList.add('shopify-section-header-sticky', 'animate');
      this.header.classList.remove('shopify-section-header-hidden');
    }

    reset() {
      this.header.classList.remove('shopify-section-header-hidden', 'shopify-section-header-sticky', 'animate');
    }

    closeMenuDisclosure() {
      this.disclosures = this.disclosures || this.header.querySelectorAll('header-menu');
      this.disclosures.forEach(disclosure => disclosure.close());
    }

    // closeSearchModal() {
    //   this.searchModal = this.searchModal || this.header.querySelector('details-modal');
    //   this.searchModal.close(false);
    // }
  }

  customElements.define('sticky-header', StickyHeader);
        
        
  class LocalizationForm extends HTMLElement {
    constructor() {
      super();
      this.elements = {
        input: this.querySelector('input[name="locale_code"], input[name="country_code"]'),
        button: this.querySelector('button'),
        panel: this.querySelector('.disclosure__list-wrapper'),
      };
      this.elements.button.addEventListener('click', this.openSelector.bind(this));
      this.elements.button.addEventListener('focusout', this.closeSelector.bind(this));
      this.addEventListener('keyup', this.onContainerKeyUp.bind(this));
      this.querySelectorAll('a').forEach(item => item.addEventListener('click', this.onItemClick.bind(this)));     
    }

    hidePanel() {
      this.elements.button.setAttribute('aria-expanded', 'false');
      this.elements.panel.setAttribute('hidden', true);
    }

    onContainerKeyUp(event) {
      if (event.code.toUpperCase() !== 'ESCAPE') return;

      this.hidePanel();
      this.elements.button.focus();
    }

    onItemClick(event) {
      event.preventDefault();
      const form = this.querySelector('form');
      this.elements.input.value = event.currentTarget.dataset.value;
      if (form) form.submit();
    }

    openSelector() {
      this.elements.button.focus();
      this.elements.panel.toggleAttribute('hidden');
      this.elements.button.setAttribute('aria-expanded', (this.elements.button.getAttribute('aria-expanded') === 'false').toString());
    }

    closeSelector(event) {
      const shouldClose = event.relatedTarget && event.relatedTarget.nodeName === 'BUTTON';
      if (event.relatedTarget === null || shouldClose) {
        this.hidePanel();
      }
    }
  }

  customElements.define('localization-form', LocalizationForm);



  
{% endjavascript %}

<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Organization",
    "name": {{ shop.name | json }},
    {% if section.settings.logo %}
      "logo": {{ section.settings.logo | image_url: width: section.settings.logo_width | prepend: "https:" | json }},
    {% endif %}
    "sameAs": [
      {{ settings.social_twitter_link | json }},
      {{ settings.social_facebook_link | json }},
      {{ settings.social_pinterest_link | json }},
      {{ settings.social_instagram_link | json }},
      {{ settings.social_tiktok_link | json }},
      {{ settings.social_tumblr_link | json }},
      {{ settings.social_snapchat_link | json }},
      {{ settings.social_youtube_link | json }},
      {{ settings.social_vimeo_link | json }}
    ],
    "url": {{ request.origin | append: page.url | json }}
  }
</script>

{%- if request.page_type == 'index' -%}
  {% assign potential_action_target = request.origin | append: routes.search_url | append: "?q={search_term_string}" %}
  <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": {{ shop.name | json }},
      "potentialAction": {
        "@type": "SearchAction",
        "target": {{ potential_action_target | json }},
        "query-input": "required name=search_term_string"
      },
      "url": {{ request.origin | append: page.url | json }}
    }
  </script>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.header.name",
  "class": "section-header",
  "settings": [
  {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },  
  
  {
  "type": "header",
  "content": "Logo" 
  },  
  {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.header.settings.logo.label"
    },
    {
      "type": "range",
      "id": "logo_width",
      "min": 50,
      "max": 250,
      "step": 10,
      "default": 100,
      "unit": "t:sections.header.settings.logo_width.unit",
      "label": "t:sections.header.settings.logo_width.label"
    },
    {
      "type": "select",
      "id": "logo_position",
      "options": [
        {
          "value": "middle-left",
          "label": "t:sections.header.settings.logo_position.options__1.label"
        },
        {
          "value": "middle",
          "label": "t:sections.header.settings.logo_position.options__2.label"
        },
        {
          "value": "top-left",
          "label": "t:sections.header.settings.logo_position.options__3.label"
        },
        {
          "value": "top-center",
          "label": "t:sections.header.settings.logo_position.options__4.label"
        }
      ],
      "default": "middle-left",
      "label": "t:sections.header.settings.logo_position.label",
      "info": "t:sections.header.settings.logo_position.info"
    },
    {
      "type": "select",
      "id": "menu_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.header.settings.menu_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.header.settings.menu_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.header.settings.menu_alignment.options__3.label"
        }
        
      ],
      "default": "center",
      "label": "t:sections.header.settings.menu_alignment.label"
    },
    {
      "type": "link_list",
      "id": "menu",
      "default": "main-menu",
      "label": "t:sections.header.settings.menu.label"
    },
    {
    "type": "checkbox",
    "id": "show_secondary_menu",
    "label": "t:sections.header.settings.show_secondary_menu.label",  
    "default": false
    },
    {
    "type": "link_list",
    "id": "secondary_menu",
    "label": "t:sections.header.settings.secondary_menu.label",
    "info": "t:sections.header.settings.secondary_menu.info",  
    "default": "main-menu"
    },
    {
    "type": "text",
    "id": "mobile_screen_width",
    "label": "t:sections.header.settings.mobile_screen_width.label",
    "default": "1280",
    "info": "Units not needed. 'px' comes by default"
    },
    {
      "type": "checkbox",
      "id": "show_line_separator",
      "default": true,
      "label": "t:sections.header.settings.show_line_separator.label"
    },
    {
      "type": "checkbox",
      "id": "enable_sticky_header",
      "default": true,
      "label": "t:sections.header.settings.enable_sticky_header.label",
      "info": "t:sections.header.settings.enable_sticky_header.info"
    },
     {
      "type": "checkbox",
      "id": "enable_transparent_header",
      "default": false,
      "label": "t:sections.header.settings.enable_transparent_header.label"
    },
   
    {
      "type": "header",
      "content": "t:sections.header.settings.header__3.content",
      "info": "t:sections.header.settings.header__4.info"
    },
    {
      "type": "checkbox",
      "id": "enable_country_selector",
      "default": true,
      "label": "t:sections.header.settings.enable_country_selector.label"
    },
    {
      "type": "header",
      "content": "t:sections.header.settings.header__5.content",
      "info": "t:sections.header.settings.header__6.info"
    },
    {
      "type": "checkbox",
      "id": "enable_language_selector",
      "default": true,
      "label": "t:sections.header.settings.enable_language_selector.label"
    },
     {
      "type": "header",
      "content": "t:sections.header.settings.header.content"
    },
    {
    "type": "radio",
    "id": "search_style",
   "label": "t:sections.header.settings.search_style.label",
    "default": "search_icon",
    "options": [
    {
    "value": "search_icon",
   "label": "t:sections.header.settings.search_style.options__1.label"
    },
    {
    "value": "search_box",
    "label": "t:sections.header.settings.search_style.options__2.label"
    },
     {
    "value": "none",
    "label": "t:sections.header.settings.search_style.options__3.label"
    }
    ]
    },        
    {
    "type": "checkbox",
    "id": "show_header_wishlist",
    "label": "t:sections.header.settings.show_header_wishlist.label",
    "default": true
    },
    {
    "type": "checkbox",
    "id": "show_header_compare",
    "label": "t:sections.header.settings.show_header_compare.label",
    "default": true
    },
    {
    "type": "checkbox",
    "id": "show_account",
    "label": "t:sections.header.settings.show_account.label",
    "default": true
    },
    {
    "type": "checkbox",
    "id": "show_header_cart",
    "label": "t:sections.header.settings.show_header_cart.label",
    "default": true
    },
    {
    "type": "radio",
    "id": "header_icon_type",
   "label": "t:sections.header.settings.header_icon_type.label",
    "default": "icon",
    "options": [
    {
    "value": "icon",
   "label": "t:sections.header.settings.header_icon_type.options__1.label"
    },
    {
    "value": "text",
    "label": "t:sections.header.settings.header_icon_type.options__2.label"
    }
    ]
    },
     {
      "type": "header",
      "content": "category Button "
    },
    {
    "type": "checkbox",
    "id": "show_category_menu",
    "label": "t:sections.header.settings.show_category_menu.label",  
    "default": false
    },
    {
    "type" : "text",
    "id" : "category_heading",
    "label" : "Category Heading",
    "default" : "All categories"
    },
     {
    "type": "link_list",
    "id": "category_menu",
    "label": "t:sections.header.settings.category_menu.label"
    },  
    {
      "type": "header",
      "content": "t:sections.all.spacing"
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.header.settings.margin_bottom.label",
      "default": 0
    },
  {
"type": "header",
"content": "Sale"
},
{
"type" : "checkbox",
"id" : "enable_sale",
"label" : "Display Sale Tag",
"default" : true
},
{
"type" : "text",
"id" : "sale_txt",
"label" : "Sale",
"default" : "Sale"
},
{
"type" : "textarea",
"id" : "sale_tags",
"label" : "Map sale tag with menus",
"info": "Separate with comma[,]"
},
{
"type": "header",
"content": "New"
},
{
"type" : "checkbox",
"id" : "enable_new",
"label" : "Display New Tag",
"default" : true
},
{
"type" : "text",
"id" : "new_txt",
"label" : "New",
"default" : "New"
},
{
"type" : "textarea",
"id" : "new_tags",
"label" : "Map new tag with menus",
"info": "Separate with comma[,]"
},
{
"type": "header",
"content": "Hot"
},
{
"type" : "checkbox",
"id" : "enable_hot",
"label" : "Display Hot Tag",
"default" : true
},
{
"type" : "text",
"id" : "hot_txt",
"label" : "Hot",
"default" : "Hot"
},
{
"type" : "textarea",
"id" : "hot_tags",
"label" : "Map hot tag with menus",
"info": "Separate with comma[,]"
},
  {
  "type": "select",
  "id": "dropdown_style",
  "label": "Dropdown style",
  "options": [
   {
  "value": "default_dropdown",
  "label": "Default (based on header width)"
  },
  {
  "value": "fullwidth_dropdown",
  "label": "Fullwidth"
  },
  {
  "value": "container_width_dropdown",
  "label": "Container width"
  },
  {
  "value": "custom_width_dropdown",
  "label": "Custom width"
  }
  ]
  }
],
"blocks": [
{
"type": "promo_image",
"name": "Promo image",
"settings": [
{
"type": "text",
"id": "mega_1",
"label": "Map item",
"info": "This submenu will appear as a megamenu."
},
{
"type": "select",
"id": "mega_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "four-column",			"label": "4 column"},
{"value": "three-column",			"label": "3 column"},
{"value": "five-column",			"label": "5 column"},
{"value": "six-column",				"label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse",
"label": "Row reverse",
"default": false
},
  {
  "type": "checkbox",
  "id": "enable_custom_width",
  "label": "Custom width for this block alone",
  "default": false
  },
  {
  "type": "range",
  "id": "custom_megamenu_width",
  "label": "Custom megamenu width",
  "min": 25,
  "max": 100,
  "step": 1,
  "unit": "%",
  "default": 75
  },
{
"type": "header",
"content": "Image 1"
},
{
"type": "image_picker",
"id": "image_1",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_1",
"label": "Title text"
},
{
"type": "url",
"id": "link_1",
"label": "Link"
},
{
"type": "range",
"id": "width_1",
"label": "Width",
"min": 50,
"max": 500,
"step": 10,
"default": 200,
"unit": "px"
},
{
"type": "range",
"id": "height_1",
"label": "Height",
"min": 50,
"max": 500,
"step": 10,
"default": 150,
"unit": "px"
},
{
"type": "range",
"id": "border_radius_1",
"label": "Border radius",
"min": 0,
"max": 50,
"step": 1,
"default": 10,
"unit": "px"
},
{
"type": "color",
"id": "background_color_1",
"label": "Background color",
"default": "#ffffff"
},
{
"type": "select",
"id": "text_alignment_1",
"label": "Text alignment",
"options": [
{"value": "top", "label": "Top"},
{"value": "middle", "label": "Middle"},
{"value": "bottom", "label": "Bottom"}
],
"default": "middle"
},
{
"type": "header",
"content": "Image 2"
},
{
"type": "image_picker",
"id": "image_2",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_2",
"label": "Title text"
},
{
"type": "url",
"id": "link_2",
"label": "Link"
},
{
"type": "range",
"id": "width_2",
"label": "Width",
"min": 50,
"max": 500,
"step": 10,
"default": 200,
"unit": "px"
},
{
"type": "range",
"id": "height_2",
"label": "Height",
"min": 50,
"max": 500,
"step": 10,
"default": 150,
"unit": "px"
},
{
"type": "range",
"id": "border_radius_2",
"label": "Border radius",
"min": 0,
"max": 50,
"step": 1,
"default": 10,
"unit": "px"
},
{
"type": "color",
"id": "background_color_2",
"label": "Background color",
"default": "#ffffff"
},
{
"type": "select",
"id": "text_alignment_2",
"label": "Text alignment",
"options": [
{"value": "top", "label": "Top"},
{"value": "middle", "label": "Middle"},
{"value": "bottom", "label": "Bottom"}
],
"default": "middle"
},
{
"type": "header",
"content": "Image 3"
},
{
"type": "image_picker",
"id": "image_3",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_3",
"label": "Title text"
},
{
"type": "url",
"id": "link_3",
"label": "Link"
},
{
"type": "range",
"id": "width_3",
"label": "Width",
"min": 50,
"max": 500,
"step": 10,
"default": 200,
"unit": "px"
},
{
"type": "range",
"id": "height_3",
"label": "Height",
"min": 50,
"max": 500,
"step": 10,
"default": 150,
"unit": "px"
},
{
"type": "range",
"id": "border_radius_3",
"label": "Border radius",
"min": 0,
"max": 50,
"step": 1,
"default": 10,
"unit": "px"
},
{
"type": "color",
"id": "background_color_3",
"label": "Background color",
"default": "#ffffff"
},
{
"type": "select",
"id": "text_alignment_3",
"label": "Text alignment",
"options": [
{"value": "top", "label": "Top"},
{"value": "middle", "label": "Middle"},
{"value": "bottom", "label": "Bottom"}
],
"default": "middle"
},
{
"type": "header",
"content": "Image 4"
},
{
"type": "image_picker",
"id": "image_4",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_4",
"label": "Title text"
},
{
"type": "url",
"id": "link_4",
"label": "Link"
},
{
"type": "range",
"id": "width_4",
"label": "Width",
"min": 50,
"max": 500,
"step": 10,
"default": 200,
"unit": "px"
},
{
"type": "range",
"id": "height_4",
"label": "Height",
"min": 50,
"max": 500,
"step": 10,
"default": 150,
"unit": "px"
},
{
"type": "range",
"id": "border_radius_4",
"label": "Border radius",
"min": 0,
"max": 50,
"step": 1,
"default": 10,
"unit": "px"
},
{
"type": "color",
"id": "background_color_4",
"label": "Background color",
"default": "#ffffff"
},
{
"type": "select",
"id": "text_alignment_4",
"label": "Text alignment",
"options": [
{"value": "top", "label": "Top"},
{"value": "middle", "label": "Middle"},
{"value": "bottom", "label": "Bottom"}
],
"default": "middle"
},
{
"type": "header",
"content": "Image 5"
},
{
"type": "image_picker",
"id": "image_5",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_5",
"label": "Title text"
},
{
"type": "url",
"id": "link_5",
"label": "Link"
},
{
"type": "range",
"id": "width_5",
"label": "Width",
"min": 50,
"max": 500,
"step": 10,
"default": 200,
"unit": "px"
},
{
"type": "range",
"id": "height_5",
"label": "Height",
"min": 50,
"max": 500,
"step": 10,
"default": 150,
"unit": "px"
},
{
"type": "range",
"id": "border_radius_5",
"label": "Border radius",
"min": 0,
"max": 50,
"step": 1,
"default": 10,
"unit": "px"
},
{
"type": "color",
"id": "background_color_5",
"label": "Background color",
"default": "#ffffff"
},
{
"type": "select",
"id": "text_alignment_5",
"label": "Text alignment",
"options": [
{"value": "top", "label": "Top"},
{"value": "middle", "label": "Middle"},
{"value": "bottom", "label": "Bottom"}
],
"default": "middle"
},
{
"type": "header",
"content": "Image 6"
},
{
"type": "image_picker",
"id": "image_6",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_6",
"label": "Title text"
},
{
"type": "url",
"id": "link_6",
"label": "Link"
},
{
"type": "range",
"id": "width_6",
"label": "Width",
"min": 50,
"max": 500,
"step": 10,
"default": 200,
"unit": "px"
},
{
"type": "range",
"id": "height_6",
"label": "Height",
"min": 50,
"max": 500,
"step": 10,
"default": 150,
"unit": "px"
},
{
"type": "range",
"id": "border_radius_6",
"label": "Border radius",
"min": 0,
"max": 50,
"step": 1,
"default": 10,
"unit": "px"
},
{
"type": "color",
"id": "background_color_6",
"label": "Background color",
"default": "#ffffff"
},
{
"type": "select",
"id": "text_alignment_6",
"label": "Text alignment",
"options": [
{"value": "top", "label": "Top"},
{"value": "middle", "label": "Middle"},
{"value": "bottom", "label": "Bottom"}
],
"default": "middle"
}
]
},
{
"type": "promo_banner",
"name": "Promo banner",
"settings": [
{
"type": "text",
"id": "mega_1",
"label": "Map item",
"info": "This submenu will appear as a megamenu."
},
{
"type": "select",
"id": "mega_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "three-column",     "label": "3 column"},
{"value": "four-column",      "label": "4 column"},
{"value": "five-column",      "label": "5 column"},
{"value": "six-column",       "label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse",
"label": "Row reverse",
"default": false
},
  {
  "type": "checkbox",
  "id": "enable_custom_width",
  "label": "Custom width for this block alone",
  "default": false
  },
  {
  "type": "range",
  "id": "custom_megamenu_width",
  "label": "Custom megamenu width",
  "min": 25,
  "max": 100,
  "step": 1,
  "unit": "%",
  "default": 75
  },
{
"type": "header",
"content": "Banner 1"
},
{
"type": "image_picker",
"id": "image_1",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_1",
"label": "Heading"
},
{
"type": "text",
"id": "button_1",
"label": "Button"
},
{
"type": "url",
"id": "link_1",
"label": "Link"
},
{
"type": "header",
"content": "Banner 2"
},
{
"type": "image_picker",
"id": "image_2",
"label": "Image",
"info": "Recommended size: 400 x 700 px"
},
{
"type": "text",
"id": "title_2",
"label": "Heading"
},
{
"type": "text",
"id": "button_2",
"label": "Button"
},
{
"type": "url",
"id": "link_2",
"label": "Link"
}
]
},
{
"type": "brands",
"name": "Brands",
"settings": [
{
"type": "text",
"id": "mega_1",
"label": "Map item",
"info": "This submenu will appear as a megamenu."
},
{
"type": "select",
"id": "mega_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "three-column",     "label": "3 column"},
{"value": "four-column",      "label": "4 column"},
{"value": "five-column",      "label": "5 column"},
{"value": "six-column",       "label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},

{
"type": "checkbox",
"id": "row_reverse",
"label": "Row reverse",
"default": false
},
  {
  "type": "checkbox",
  "id": "enable_custom_width",
  "label": "Custom width for this block alone",
  "default": false
  },
  {
  "type": "range",
  "id": "custom_megamenu_width",
  "label": "Custom megamenu width",
  "min": 25,
  "max": 100,
  "step": 1,
  "unit": "%",
  "default": 75
  },
{
"type": "image_picker",
"id": "image_1",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_1_text",
"label": "Title"
},
{
"type": "url",
"id": "link_1",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_2",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_2_text",
"label": "Title"
},
{
"type": "url",
"id": "link_2",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_3",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_3_text",
"label": "Title"
},
{
"type": "url",
"id": "link_3",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_4",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_4_text",
"label": "Title"
},
{
"type": "url",
"id": "link_4",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_5",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_5_text",
"label": "Title"
},
{
"type": "url",
"id": "link_5",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_6",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_6_text",
"label": "Title"
},
{
"type": "url",
"id": "link_6",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_7",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_7_text",
"label": "Title"
},
{
"type": "url",
"id": "link_7",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_8",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_8_text",
"label": "Title"
},
{
"type": "url",
"id": "link_8",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_9",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
  {
"type": "text",
"id": "link_9_text",
"label": "Title"
},
{
"type": "url",
"id": "link_9",
"label": "Link"
},
{
"type": "image_picker",
"id": "image_10",
"label": "Brand",
"info": "Recommended size: 250 x 200 px"
},
{
"type": "text",
"id": "link_10_text",
"label": "Title"
},
{
"type": "url",
"id": "link_10",
"label": "Link"
}
]
},
{
"type": "product",
"name": "Product",
"settings": [
{
"type": "text",
"id": "mega_1",
"label": "Map item",
"info": "This submenu will appear as a megamenu."
},
{
"type": "select",
"id": "mega_columns",
"label":"Column style",
"default": "four-column",
"options":  [
{"value": "three-column",			"label": "3 column"},
{"value": "four-column",			"label": "4 column"},
{"value": "five-column",			"label": "5 column"},
{"value": "six-column",				"label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse",
"label": "Row reverse",
"default": false
},
  {
  "type": "checkbox",
  "id": "enable_custom_width",
  "label": "Custom width for this block alone",
  "default": false
  },
  {
  "type": "range",
  "id": "custom_megamenu_width",
  "label": "Custom megamenu width",
  "min": 25,
  "max": 100,
  "step": 1,
  "unit": "%",
  "default": 75
  },
{
"type": "product",
"id": "product1",
"label": "Product"
},
{
"type": "product",
"id": "product2",
"label": "Product"
},
{
"type": "product",
"id": "product3",
"label": "Product"
},
{
"type": "product",
"id": "product4",
"label": "Product"
}
]
},
{
"type": "tab",
"name": "Tab menu",
"settings": [
{
"type": "text",
"id": "mega_1",
"label": "Map item",
"info": "This submenu will appear as a megamenu."
},
{
"type": "checkbox",
"id": "row_reverse",
"label": "Row reverse",
"default": false
},
  {
  "type": "checkbox",
  "id": "enable_custom_width",
  "label": "Custom width for this block alone",
  "default": false
  },
  {
  "type": "range",
  "id": "custom_megamenu_width",
  "label": "Custom megamenu width",
  "min": 25,
  "max": 100,
  "step": 1,
  "unit": "%",
  "default": 75
  },
{
"type": "select",
"id": "tab-menu",
"label": "Tab Menu Style",
"default": "horizontal",
"options": [
{"value": "horizontal", "label": "Horizontal"},
{"value": "vertical",	"label": "Vertical"}
]
},
{
"type": "header",
"content": "Tab 1"
},
{
"type": "text",
"id": "tab1_heading",
"label": "Tab Heading",
"default": "Headding"
},
{
"type": "link_list",
"id": "tab1",
"label": "Select Menu"
},
{
"type": "select",
"id": "tab1_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "three-column",			"label": "3 column"},
{"value": "four-column",			"label": "4 column"},
{"value": "five-column",			"label": "5 column"},
{"value": "six-column",				"label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse_tab1",
"label": "Row reverse",
"default": false
},
{
"type": "image_picker",
"id": "tab1_image",
"label": "Banner"
},
{
"type": "header",
"content": "Tab 2"
},
{
"type": "text",
"id": "tab2_heading",
"label": "Tab Heading",
"default": "Headding"
},
{
"type": "link_list",
"id": "tab2",
"label": "Select Menu"
},
{
"type": "select",
"id": "tab2_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "three-column",			"label": "3 column"},
{"value": "four-column",			"label": "4 column"},
{"value": "five-column",			"label": "5 column"},
{"value": "six-column",				"label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse_tab2",
"label": "Row reverse",
"default": false
},
{
"type": "image_picker",
"id": "tab2_image",
"label": "Banner"
},
{
"type": "header",
"content": "Tab 3"
},
{
"type": "text",
"id": "tab3_heading",
"label": "Tab Heading",
"default": "Headding"
},
{
"type": "link_list",
"id": "tab3",
"label": "Select Menu"
},
{
"type": "select",
"id": "tab3_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "three-column",			"label": "3 column"},
{"value": "four-column",			"label": "4 column"},
{"value": "five-column",			"label": "5 column"},
{"value": "six-column",				"label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse_tab3",
"label": "Row reverse",
"default": false
},
{
"type": "image_picker",
"id": "tab3_image",
"label": "Banner"
},
{
"type": "header",
"content": "Tab 4"
},
{
"type": "text",
"id": "tab4_heading",
"label": "Tab Heading",
"default": "Headding"
},
{
"type": "link_list",
"id": "tab4",
"label": "Select Menu"
},

{
"type": "select",
"id": "tab4_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "three-column",			"label": "3 column"},
{"value": "four-column",			"label": "4 column"},
{"value": "five-column",			"label": "5 column"},
{"value": "six-column",				"label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse_tab4",
"label": "Row reverse",
"default": false
},
{
"type": "image_picker",
"id": "tab4_image",
"label": "Banner"
},
{
"type": "header",
"content": "Tab 5"
},
{
"type": "text",
"id": "tab5_heading",
"label": "Tab Heading",
"default": "Headding"
},
{
"type": "link_list",
"id": "tab5",
"label": "Select Menu"
},
{
"type": "select",
"id": "tab5_columns",
"label":"Column style",
"default": "four-column",
"options": [
{"value": "three-column",			"label": "3 column"},
{"value": "four-column",			"label": "4 column"},
{"value": "five-column",			"label": "5 column"},
{"value": "six-column",				"label": "6 column"},
{"value": "double-quarter-one-half","label": "1:1:2 column"}
]
},
{
"type": "checkbox",
"id": "row_reverse_tab5",
"label": "Row reverse",
"default": false
},
{
"type": "image_picker",
"id": "tab5_image",
"label": "Banner"
},
{
"type": "header",
"content": "Tab Colors"
}
]
}
]
}
{% endschema %}
