{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

<link rel="stylesheet" href="{{ 'component-slider.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'template-collection.css' | asset_url }}" media="print" onload="this.media='all'">
{%- if settings.enable_quickadd or settings.enable_quickview -%}
  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'template-collection.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  /* Header layout matching reference design */
  .section-{{ section.id }} .title-wrapper-with-link {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 2rem;
    gap: 1rem;
    {% if section.settings.column_alignment == 'left' %}
      justify-content: space-between !important;
      flex-direction: row !important;
    {% elsif section.settings.column_alignment == 'center' %}
      justify-content: center !important;
      flex-direction: column !important;
      text-align: center !important;
    {% elsif section.settings.column_alignment == 'right' %}
      justify-content: flex-start !important;
      flex-direction: row-reverse !important;
    {% else %}
      justify-content: space-between !important;
      flex-direction: row !important;
    {% endif %}
  }

  .section-{{ section.id }} .title {
    margin: 0 !important;
    font-weight: 500;
    font-size: 24px;
    line-height: 1.2;
  }

  /* View All button styling to match reference */
  .section-{{ section.id }} .view-all-top a {
    font-size: 14px;
    padding: 8px 16px;
    border: 1px solid #000;
    color: #000;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 400;
  }

  .section-{{ section.id }} .view-all-top a:hover {
    background-color: #000;
    color: #fff;
  }

  .section-{{ section.id }} .title-content {
    {% if section.settings.column_alignment == 'left' %}
      flex: 1;
      min-width: 0;
      text-align: left;
    {% elsif section.settings.column_alignment == 'center' %}
      width: 100%;
      text-align: center;
    {% elsif section.settings.column_alignment == 'right' %}
      order: 2;
      flex: 1;
      text-align: right;
    {% else %}
      flex: 1;
      min-width: 0;
      text-align: left;
    {% endif %}
  }

  .section-{{ section.id }} .view-all-top {
    {% if section.settings.column_alignment == 'left' %}
      flex-shrink: 0;
    {% elsif section.settings.column_alignment == 'center' %}
      width: 100%;
      text-align: center;
      margin-top: 1rem;
    {% elsif section.settings.column_alignment == 'right' %}
      order: 1;
      margin-right: 1rem;
      flex-shrink: 0;
    {% else %}
      flex-shrink: 0;
    {% endif %}
  }

  /* Mobile specific styles */
  @media screen and (max-width: 749px) {
    .section-{{ section.id }} .title-wrapper-with-link {
      flex-direction: column !important;
      align-items: stretch !important;
      gap: 1.5rem;
    }

    .section-{{ section.id }} .title-content {
      {% if section.settings.column_alignment == 'right' %}
        order: 2;
        text-align: right;
      {% elsif section.settings.column_alignment == 'center' %}
        order: 1;
        text-align: center;
      {% else %}
        order: 1;
        text-align: left;
      {% endif %}
      width: 100%;
    }

    .section-{{ section.id }} .view-all-top {
      {% if section.settings.column_alignment == 'right' %}
        order: 1;
        align-self: flex-end;
      {% elsif section.settings.column_alignment == 'center' %}
        order: 2;
        align-self: center;
      {% else %}
        order: 2;
        align-self: flex-end;
      {% endif %}
      margin: 0;
    }

    .section-{{ section.id }} .title {
      font-size: 20px;
    }
  }

  /* Tablet and Desktop styles */
  @media screen and (min-width: 576px) {
    .section-{{ section.id }} .title-wrapper-with-link {
      {% if section.settings.column_alignment == 'left' %}
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
      {% elsif section.settings.column_alignment == 'center' %}
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
      {% elsif section.settings.column_alignment == 'right' %}
        flex-direction: row-reverse !important;
        justify-content: flex-start !important;
        align-items: center !important;
      {% else %}
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
      {% endif %}
    }

    .section-{{ section.id }} .view-all-top a {
      white-space: nowrap;
    }
  }

  /* Product carousel styles */
  .section-{{ section.id }} .product-grid {
    gap: 1rem;
  }

  .section-{{ section.id }} .slider {
    overflow-x: auto;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .section-{{ section.id }} .slider::-webkit-scrollbar {
    display: none;
  }

  .section-{{ section.id }} .slider {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Ensure proper spacing and layout */
  .section-{{ section.id }} .collection {
    max-width: 100%;
  }

  /* Product card spacing in carousel */
  .section-{{ section.id }} .grid__item {
    min-width: 280px;
    flex-shrink: 0;
  }

  @media screen and (max-width: 749px) {
    .section-{{ section.id }} .grid__item {
      min-width: 250px;
    }
  }

{%- endstyle -%}

{%- liquid
  assign products_to_display = section.settings.collection.all_products_count

  if section.settings.collection.all_products_count > section.settings.products_to_show
    assign products_to_display = section.settings.products_to_show
    assign more_in_collection = true
  endif

  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and products_to_display > columns_mobile_int
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider and products_to_display > section.settings.columns_desktop
    assign show_desktop_slider = true
  endif

  if section.settings.swiper_enable
  assign enable_slider = true  
  endif
  
-%}

<div class="color-{{ section.settings.color_scheme }} isolate gradient">
  <div class="collection {% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} {{ section.settings.custom_class_name }} section-{{ section.id }}-padding">
   <div class="row">
    {%- unless section.settings.title == blank and section.settings.sub_heading == blank and section.settings.description == blank and section.settings.button_label == blank and section.settings.show_view_all == false -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
        <div class="title-content">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
        </div>
        {%- if section.settings.show_view_all and section.settings.collection != blank -%}
          <div class="view-all-top">
            <a href="{{ section.settings.collection.url }}"
              aria-label="{{ 'sections.featured_collection.view_all_label' | t: collection_name: section.settings.collection.title }}"
            >
              {{ 'sections.featured_collection.view_all' | t }}
            </a>
          </div>
        {%- endif -%}
      </div>
    {%- endunless -%}

    {% unless enable_slider %}
    <slider-component class="slider-mobile-gutter{% if section.settings.full_width %} slider-component-full-width{% endif %}{% if show_desktop_slider %} slider-component-desktop{% endif %}">
    {% else %}
    <featured-swiper-slider>
      <div data-slider-options='{"loop": "{%- if section.settings.centered_slide -%}1{%- else -%}2{%- endif -%}","desktop": "{{ section.settings.desktop_column }}", "laptop": "{{ section.settings.laptop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
      <div class="swiper" data-swiper-slider>
      {%- endunless -%}
      <div id="Slider-{{ section.id }}" class="product-grid contains-card {% if enable_slider %} swiper-wrapper{% else %} grid grid--{{ section.settings.columns_desktop }}-col-desktop{% if section.settings.collection == blank %} grid--2-col-tablet-down{% else %} grid--{{ section.settings.columns_mobile }}-col-tablet-down{% endif %}{% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--tablet grid--peek{% endif %}{% endif %}{% endif %}" role="list" aria-label="{{ 'sections.featured_collection.slider' | t }}">
        {%- for product in section.settings.collection.products limit: section.settings.products_to_show -%}
          <div id="Slide-{{ section.id }}-{{ forloop.index }}" class="{% if enable_slider %} swiper-slide{% else %} grid__item{% if show_mobile_slider or show_desktop_slider %} slider__slide{% endif %}{% endif %} card_style-{{ settings.card_style }}">
          {%  case settings.card_style %}
          {%  when 'standard' %}  
            {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                        {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
            {% render 'card-product',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
               placeholder_image: placeholder_image,
              section_id: section.id
            %}
        {%  when 'button_width_icons' %}  
             {% render 'card-product-2',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_icons' %} 
             {% render 'card-product-3',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_buttons' %}  
             {% render 'card-product-4',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_overlay' %}  
             {% render 'card-product-5',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  endcase %}
          </div>
        {%- else -%}
          {%- for i in (1..4) -%}
            <div class="swiper-slide">
              {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                        {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
              {% render 'card-product', show_vendor: section.settings.show_vendor, placeholder_image: placeholder_image %}
            </div>
          {%- endfor -%}
        {%- endfor -%}
      </div>
      {% unless enable_slider %}
      {%- if show_mobile_slider or show_desktop_slider -%}
        {% if section.settings.arrow_on_mobile %}
        <div class="slider-buttons no-js-hidden">
          <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-caret' %}</button>
          <div class="slider-counter caption">
          </div>
          <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-caret' %}</button>
        </div>
      {% endif %}
      {%- endif -%}
    </slider-component>
    {% else %}
        {% if section.settings.swiper_navigation != blank %}
        <div class="swiper-button-next"><span></span></div>
        <div class="swiper-button-prev"><span></span></div>
        {% endif %}
    </div>
    {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}
    </div>
    </featured-swiper-slider>
    {%- endunless -%}

  </div>
</div>
</div>
{% schema %}
{
  "name": "t:sections.featured-collection.name",
  "tag": "section",
  "class": "section section-featured-collection",
  "settings": [
      {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
      {
      "type": "text",
      "id": "title",
      "default": "Featured Collection",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default":"Sub Heading",
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default":"Use this text to share the information which you like!.",
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.featured-collection.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.featured-collection.settings.column_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left",
      "label": "t:sections.featured-collection.settings.column_alignment.label"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "t:sections.featured-collection.settings.collection.label"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 12,
      "step": 1,
      "default": 4,
      "label": "t:sections.featured-collection.settings.products_to_show.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "t:sections.featured-collection.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "t:sections.featured-collection.settings.show_view_all.label"
    },

    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info"
    },
    {
      "type": "header",
      "content": "t:sections.featured-collection.settings.header.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.featured-collection.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.featured-collection.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.featured-collection.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.featured-collection.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_new_tag",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_new_tag.label"
    },    
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.featured-collection.settings.show_rating.label",
      "info": "t:sections.featured-collection.settings.show_rating.info"
    },    
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "checkbox",
      "id": "swiper_enable",
      "default": false,
      "label": "t:sections.all.swiper.swiper_slider_enable"
    },
    {
      "type": "range",
      "id": "desktop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.desktop_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "laptop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.laptop_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "tablet_column",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.tablet_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "mobile_column",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "t:sections.all.swiper.mobile_column",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "centered_slide",
      "default": false,
      "label": "t:sections.all.swiper.centered_slide"
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.featured-collection.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "arrow_on_mobile",
      "default": false,
      "label": "t:sections.all.swiper.controls"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.all.swiper.options__1"
        },
        {
          "value": "2",
          "label": "t:sections.all.swiper.options__2"
        }
      ],
      "default": "1",
      "label": "t:sections.all.swiper.columns_mobile"
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-collection.presets.name"
    }
  ]
}
{% endschema %}
