{"sections": {"otsb_circle_menu_2_RNkPHm": {"type": "otsb-circle-menu-2", "blocks": {"featured_collection_fT3RAB": {"type": "featured_collection", "settings": {"image_menu_item": "shopify://shop_images/Screenshot_2025-09-07_153219.png", "url_menu_item": "shopify://collections/new1", "title": "Baby birth frame"}}, "featured_collection_dxceA6": {"type": "featured_collection", "settings": {"image_menu_item": "shopify://shop_images/Screenshot_2025-09-07_153241.png", "url_menu_item": "shopify://collections", "title": "Anniversary gifts"}}, "featured_collection_RaMjbb": {"type": "featured_collection", "settings": {"image_menu_item": "shopify://shop_images/Screenshot_2025-09-07_153301.png", "url_menu_item": "shopify://collections", "title": "Trending gifts"}}, "featured_collection_tjEdi4": {"type": "featured_collection", "settings": {"image_menu_item": "shopify://shop_images/Gemini_Generated_Image_duz9jiduz9jiduz9_4_725497e9-5705-415a-b788-f683d278de9a.png", "url_menu_item": "shopify://collections", "title": "Leather wallets"}}, "featured_collection_UJFeYj": {"type": "featured_collection", "settings": {"image_menu_item": "shopify://shop_images/Screenshot_2025-09-07_153435.png", "url_menu_item": "", "title": "Wall clocks"}}}, "block_order": ["featured_collection_fT3RAB", "featured_collection_dxceA6", "featured_collection_<PERSON><PERSON><PERSON><PERSON>", "featured_collection_tjEdi4", "featured_collection_UJFeYj"], "custom_css": [".otsb_trademark_root {font-size: 0 !important;}", ".splide__arrow--prev {display: none !important;}", ".splide__arrow--next {display: none !important;}", " /* Fallback if Shopify strips long class names */a.collection-card:hover div:last-child {color: gold !important; transition: color 0.3s ease;}"], "name": "OT: Circle menu #2", "settings": {"show_section": "show_both", "heading": "", "highlight_type": "font_highlight", "heading_size": 100, "heading_tag": "h2", "sub_heading": "", "text_line_height": "inherit", "use_custom_font": false, "type_header_font": "poppins_n6", "text_font": "poppins_n4", "content_alignment": "center", "content_item": "center", "use_custom_title_font": false, "title_font": "poppins_n6", "title_size": 80, "title_alignment": "center", "title_height": "inherit", "title_top_spacing": 14, "border_item": true, "enable_hover_effect": true, "border_thickness": 2, "image_border_spacing": 5, "image_border_spacing_mobile": 3, "lazy_load_image": true, "slider_arrow": "top_right", "width_height_button": 45, "show_pagination_mobile": true, "slider_visual": "none", "item_size": 138, "item_spacing": 40, "menu_block_width": 100, "make_section_full_width": false, "show_section_divider": false, "padding_top_bottom": 28, "right_padding": 52, "left_padding": 52, "element_spacing": 20, "item_size_mobile": 100, "item_spacing_mobile": 20, "menu_block_width_mobile": 100, "width_section_mobile": true, "show_section_divider_mobile": false, "padding_top_bottom_mobile": 28, "right_padding_mobile": 0, "left_padding_mobile": 20, "element_spacing_mobile": 30, "background_color": "", "color_heading": "rgba(0,0,0,0)", "text_color": "rgba(0,0,0,0)", "text_link": "rgba(0,0,0,0)", "marker_color": "#e1a832", "title_text_color": "rgba(0,0,0,0)", "border_color_gradient": "linear-gradient(180deg, rgba(225, 168, 50, 1), rgba(220, 70, 19, 1) 100%)", "hover_border_color_gradient": "linear-gradient(180deg, rgba(225, 168, 50, 1), rgba(220, 70, 19, 1) 100%)", "background_item_color": "linear-gradient(180deg, rgba(201, 201, 201, 1), rgba(201, 201, 201, 1) 100%)", "arrow_button_color": "#ededed", "arrow_button_hover_color": "rgba(0,0,0,0)", "arrow_color": "#c5c5c5", "section_divider_color": "rgba(0,0,0,0)", "pagination_icon_color": "#e1a832"}}, "otsb_slideshow_4_QEUGY9": {"type": "otsb-slideshow-4", "blocks": {"slide_J7ttP7": {"type": "slide", "settings": {"image": "shopify://shop_images/Gemini_Generated_Image_arlck5arlck5arlc.png", "overlay_opacity": 0, "image_overlay_color": "#202020", "slide_link": "", "open_new_window_slide": false, "show_sound_control": false, "video_url": "", "enable_video_autoplay": false, "video_alt_text": "", "enable_bakground": false, "subheading": "", "heading": "Gifts That Speak Love", "highlight_type": "underline", "heading_size": 170, "heading_tag": "h2", "text": "<p><br/>Every gift, a memory. Every moment, a story.<br/><br/></p>", "button_type": "rounded", "button_color_mobile": "color", "button_animation": "sliced", "custom_icon_button": "", "button_label_1": "Shop Now", "button_link_1": "", "open_new_window_button": false, "button_primary_1": true, "button_label_2": "", "button_link_2": "", "open_new_window_button_2": false, "button_primary_2": false, "content_alignment": "middle-left", "custom_horizontal": 85, "custom_vertical": 80, "alignment": "left", "mobile_custom_position": false, "custom_horizontal_mobile": 50, "custom_vertical_mobile": 50, "alignment_mobile": "left", "heading_color": "#000000", "color_heading_highlight": "rgba(0,0,0,0)", "color_text": "#000000", "color_text_link": "#ffffff", "color_button": "#f07c22", "color_text_button": "#ffffff", "color_button_hover": "#f07c22", "color_text_button_hover": "#ffffff", "color_button_secondary": "", "secondary_button_text": ""}}, "slide_h7QcW6": {"type": "slide", "settings": {"image": "shopify://shop_images/Picsart_25-09-07_14-50-04-180.png", "overlay_opacity": 0, "image_overlay_color": "#202020", "slide_link": "", "open_new_window_slide": false, "show_sound_control": false, "video_url": "", "enable_video_autoplay": false, "video_alt_text": "", "enable_bakground": false, "subheading": "", "heading": "Because Their First Day Deserves Forever", "highlight_type": "underline", "heading_size": 170, "heading_tag": "h2", "text": "<p>Every smile, every moment, every memory — captured in a frame as precious as your little one’s first day.</p>", "button_type": "rounded", "button_color_mobile": "color", "button_animation": "sliced", "custom_icon_button": "", "button_label_1": "View All", "button_link_1": "", "open_new_window_button": false, "button_primary_1": true, "button_label_2": "", "button_link_2": "", "open_new_window_button_2": false, "button_primary_2": false, "content_alignment": "custom", "custom_horizontal": 85, "custom_vertical": 80, "alignment": "right", "mobile_custom_position": false, "custom_horizontal_mobile": 50, "custom_vertical_mobile": 50, "alignment_mobile": "center", "heading_color": "#000000", "color_heading_highlight": "rgba(0,0,0,0)", "color_text": "#000000", "color_text_link": "#ffffff", "color_button": "#e1680b", "color_text_button": "#f8f8f8", "color_button_hover": "#f07c22", "color_text_button_hover": "#ffffff", "color_button_secondary": "", "secondary_button_text": ""}}, "slide_dnEj8e": {"type": "slide", "settings": {"image": "shopify://shop_images/Picsart_25-09-07_15-14-31-718.png", "overlay_opacity": 0, "image_overlay_color": "#202020", "slide_link": "", "open_new_window_slide": false, "show_sound_control": false, "video_url": "", "enable_video_autoplay": false, "video_alt_text": "", "enable_bakground": false, "subheading": "", "heading": "Every Gift, a Little Story of Its Own", "highlight_type": "underline", "heading_size": 170, "heading_tag": "h2", "text": "<p>Because the right gift doesn’t just say something</p><p> it feels something.</p>", "button_type": "rounded", "button_color_mobile": "color", "button_animation": "sliced", "custom_icon_button": "", "button_label_1": "Expolore More", "button_link_1": "", "open_new_window_button": false, "button_primary_1": true, "button_label_2": "", "button_link_2": "", "open_new_window_button_2": false, "button_primary_2": false, "content_alignment": "middle-center", "custom_horizontal": 85, "custom_vertical": 80, "alignment": "center", "mobile_custom_position": false, "custom_horizontal_mobile": 50, "custom_vertical_mobile": 50, "alignment_mobile": "center", "heading_color": "#000000", "color_heading_highlight": "rgba(0,0,0,0)", "color_text": "#100f0f", "color_text_link": "#ffffff", "color_button": "#e1680b", "color_text_button": "#ffffff", "color_button_hover": "#f07c22", "color_text_button_hover": "#ffffff", "color_button_secondary": "", "secondary_button_text": ""}}}, "block_order": ["slide_J7ttP7", "slide_h7QcW6", "slide_dnEj8e"], "name": "OT: Slideshow #4", "settings": {"show_hero": false, "auto_play": true, "change_slides_speed": 5, "show_arrow": false, "slider_button_color": "#525151", "slider_button_text_color": "#525151", "slider_button_hover_color": "#ff6600", "slider_button_hover_text_color": "#000000", "transition_style": "fade", "slider_visual": "dots", "full_width": true, "padding_full_width": false, "rounded_corner_image": false, "desktop_height": "550", "padding_top": 0, "padding_bottom": 28, "full_width_mobile": true, "rounded_corner_image_mobile": false, "mobile_height": "500", "padding_top_mobile": 0, "padding_bottom_mobile": 20}}, "tsapb_ca577056_CdhLLr": {"type": "tsapb-ca577056", "blocks": {"collection_EXi9Xp": {"type": "collection", "settings": {"collection": "", "heading": "Baby Birth Frame", "image_desktop": "shopify://shop_images/Gemini_Generated_Image_duz9jiduz9jiduz9_3.png"}}, "collection_g3tBpA": {"type": "collection", "settings": {"collection": "", "heading": "Anniversary Gifts", "image_desktop": "shopify://shop_images/Gemini_Generated_Image_duz9jiduz9jiduz9.png"}}, "collection_aq784B": {"type": "collection", "settings": {"collection": "", "heading": "Trending Gifts"}}, "collection_UFLmzy": {"type": "collection", "settings": {"collection": "", "heading": "<PERSON><PERSON>et", "image_desktop": "shopify://shop_images/Gemini_Generated_Image_duz9jiduz9jiduz9_4.png", "image_mobile": "shopify://shop_images/Gemini_Generated_Image_duz9jiduz9jiduz9_2.png"}}, "collection_P4WVEy": {"type": "collection", "settings": {"collection": "", "heading": "Wall Clocks", "image_desktop": "shopify://shop_images/wall-clock.png"}}}, "block_order": ["collection_EXi9Xp", "collection_g3tBpA", "collection_aq784B", "collection_UFLmzy", "collection_P4WVEy"], "disabled": true, "name": "❤️ Square: Collection Circles", "settings": {"heading": "Category Section", "heading_size": "32", "heading_style": "regular", "heading_alignment": "center", "collection_heading_size": "18", "layout_desktop": "carousel", "collections_per_row_desktop": 5, "layout_mobile": "carousel", "collections_per_row_mobile": "2", "section_width": 90, "section_max_width": "", "vertical_padding": 40, "vertical_margin": 0, "vertical_padding_mobile": 20, "vertical_margin_mobile": 0, "section_background": "", "heading_color": "#222222", "collection_heading_color": "#222222", "images_border_color": ""}}, "c81d40c6-6c76-4fd0-9717-f3423af6c9c2": {"type": "suriya-grid-banner-2", "blocks": {"608924fe-f0b6-458f-809b-b1bc5ccb7855": {"type": "text", "settings": {"block_image": "shopify://shop_images/Grid-1.webp", "image_link": "shopify://collections/boots", "show_content": true, "block_title": "Baby Birth Frames", "enable_title_link": true, "block_sub_title": "", "block_description": "", "block_button_text": "", "block_button_link": "", "desktop_content_alignment": "center", "reverse_column": false}}, "627a8786-7541-433b-9da7-5bf842f372bd": {"type": "text", "settings": {"image_link": "shopify://collections/loafer", "show_content": true, "block_title": "Anniversary Gifts", "enable_title_link": true, "block_sub_title": "", "block_description": "", "block_button_text": "", "block_button_link": "", "desktop_content_alignment": "center", "reverse_column": false}}, "33478f57-9990-41cd-8a02-57effa53a5b8": {"type": "text", "settings": {"image_link": "shopify://collections/sports", "show_content": true, "block_title": "Trending Gifts", "enable_title_link": true, "block_sub_title": "", "block_description": "", "block_button_text": "", "block_button_link": "", "desktop_content_alignment": "center", "reverse_column": false}}, "5dd73661-71d8-426d-99b3-9cc86d0b03f1": {"type": "text", "settings": {"image_link": "shopify://collections/formal", "show_content": true, "block_title": "Leather Wallets", "enable_title_link": true, "block_sub_title": "", "block_description": "", "block_button_text": "", "block_button_link": "", "desktop_content_alignment": "center", "reverse_column": false}}, "f2ca7d81-ad7e-4aad-9478-0aa22bbe52b0": {"type": "text", "settings": {"image_link": "shopify://collections/boots", "show_content": true, "block_title": "Wall Clocks", "enable_title_link": true, "block_sub_title": "", "block_description": "", "block_button_text": "", "block_button_link": "", "desktop_content_alignment": "center", "reverse_column": false}}}, "block_order": ["608924fe-f0b6-458f-809b-b1bc5ccb7855", "627a8786-7541-433b-9da7-5bf842f372bd", "33478f57-9990-41cd-8a02-57effa53a5b8", "5dd73661-71d8-426d-99b3-9cc86d0b03f1", "f2ca7d81-ad7e-4aad-9478-0aa22bbe52b0"], "disabled": true, "settings": {"page_full_width": false, "page_full_width_spacing": false, "title": "CATEGORIES", "heading_size": "h1", "sub_heading": "", "description": "orem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. ", "button_label": "View all Categories", "button_link": "", "button_style_secondary": false, "column_alignment": "center", "background_style": "primary", "banner_style": "grid", "grid-column": "5", "column_gap": "30", "overlay_height": 300, "overlay_height_laptop": 300, "overlay_height_tab": 300, "overlay_height_mobile": 200, "color_scheme": "background-1", "padding_top": 108, "padding_bottom": 0, "custom_class_name": "home-custom-grid-banner", "swiper_enable": true, "desktop_column": 5, "laptop_column": 3, "tablet_column": 2, "mobile_column": 1, "centered_slide": false, "swiper_pagination": false, "swiper_navigation": true, "auto_play": 0}}, "1757166247f04c902a": {"type": "apps", "settings": {"include_margins": true}}, "otsb_featured_collection_4_3N3rtP": {"type": "otsb-featured-collection-4", "blocks": {"collection_bKhxC6": {"type": "collection", "settings": {"collection": "new1", "show_description": true, "description": "", "number_of_lines_shown": "none", "read_more_label": "", "see_less_label": "", "show_vendor": false, "show_promotion": true, "promotion_position": "left", "card_link": "", "open_new_window": false, "image": "shopify://shop_images/download.jpg", "enable_text_overlay": true, "overlay_opacity": 0, "image_ratio": "100", "show_sound_control": false, "video_url": "", "enable_video_autoplay": false, "video_alt_text": "", "product_card": "", "heading": "Fresh Finds, Just In", "highlight_type": "underline", "heading_size": 70, "content": "<p><br/>Discover our newest arrivals crafted with love and made to impress.<br/><br/></p>", "text_size": 100, "content_alignment": "left", "content_position": "bottom", "button_label": "", "button_link": "shopify://collections/new1", "open_new_window_button": true, "show_button_style": "primary", "button_type": "rounded", "button_color_mobile": "hover", "button_animation": "fill_up", "custom_icon_button": "", "background_color": "rgba(0,0,0,0)", "color_text": "#ffffff", "color_heading_highlight": "rgba(0,0,0,0)", "color_button": "#e1680b", "color_text_button": "#f8f8f8", "color_button_hover": "rgba(0,0,0,0)", "color_text_button_hover": "rgba(0,0,0,0)", "color_button_secondary": "rgba(0,0,0,0)", "secondary_button_text": "rgba(0,0,0,0)", "colors_text_link": "rgba(0,0,0,0)"}}}, "block_order": ["collection_bKhxC6"], "custom_css": [".otsb_trademark_root {font-size: 0 !important; color: transparent !important;}", " /* Rotate only the next (right) arrow */.splide__arrow--next svg {transform: rotate(180deg);}"], "name": "OT: Featured Collection 4", "settings": {"title": "New Arrivals", "highlight_type": "underline", "heading_size": 100, "heading_tag": "h2", "heading_alignment": "center", "subheading": "", "text": "", "show_view_all": false, "view_all_position": "top", "edges_type": "rounded_corners", "lazy_image": false, "title_size": 120, "title_style": "none", "card_product_background_color_light": "rgba(0,0,0,0)", "title_product_cart": 100, "card_product_padding": 20, "card_product_padding_mobile": 5, "product_image_ratio": "3/4", "product_image_type": "hover_to_reveal_second_image", "number_of_additional_images": 3, "transition_arrows": "#000000", "show_arrow": false, "change_slides_speed_card": 2, "info_alignment_card_product": "center", "products_color_swatches_enable_on_collection_page": true, "replace_color_with_variant_images": false, "swatches_type": "both_text_and_color", "color_option_name": "Color, Colour, Couleur, Farbe", "color_option_values": "", "text_option_name": "Size, Taille, Taglia, Größe", "text_option_values": "", "products_color_swatches_style": "round", "swatch_size": "small", "swatch_show_by_default": 0, "enable_desktop_slider": true, "slider_style": "horizontal", "swiper_on_mobile": true, "auto_play": true, "change_slides_speed": 5, "columns_desktop": 4, "rows_desktop": 2, "spacing": 20, "full_width": true, "show_section_divider": false, "padding_top": 28, "padding_bottom": 28, "columns_mobile": "2", "spacing_mobile": 16, "full_width_mobile": false, "show_section_divider_mobile": false, "padding_top_mobile": 28, "padding_bottom_mobile": 28, "color_heading": "rgba(0,0,0,0)", "color_heading_highlight": "rgba(0,0,0,0)", "color_text": "rgba(0,0,0,0)", "color_text_link": "rgba(0,0,0,0)", "image_treatment_overlay": "#202020", "image_treatment_text": "#ffffff", "background_active": "#f2f2f2", "text_active": "#154326", "line_border": "rgba(0,0,0,0)"}}, "custom_liquid_Mrr7Dt": {"type": "custom-liquid", "name": "t:sections.custom-liquid.presets.name", "settings": {"page_full_width": false, "page_full_width_spacing": false, "title": "", "heading_size": "h1", "sub_heading": "", "description": "", "button_label": "show", "button_link": "", "button_style_secondary": false, "column_alignment": "center", "custom_liquid": "", "color_scheme": "background-1", "padding_top": 40, "padding_bottom": 52}}, "tsapb_c3ee5db1_VN7HtM": {"type": "tsapb-c3ee5db1", "blocks": {"caption_3RACaT": {"type": "caption", "settings": {"caption": "", "caption_size": "16", "caption_weight": "400", "caption_color": "#222222", "caption_alternative_font": false, "caption_font": "sans_serif_n4"}}, "heading_jTDH3L": {"type": "heading", "settings": {"heading": "Your Perfect Gift is Just a Click Away", "heading_size": "32", "heading_style": "regular", "heading_color": "#222222", "heading_alternative_font": false, "heading_font": "sans_serif_n4"}}, "text_BDJTzw": {"type": "text", "settings": {"description": "<p>Shop now and make every moment special.</p>", "description_size": "16", "description_color": "#222222", "description_alternative_font": false, "description_font": "sans_serif_n4"}}, "buttons_zEq6pA": {"type": "buttons", "settings": {"primary_button_behaviour": "scroll_to_top", "primary_button_label": "Shop now", "primary_button_link": "", "primary_button_style": "solid", "primary_text_color": "#ffffff", "primary_background_color": "#e1680b", "primary_border_color": "#222222", "primary_button_corner": 30, "primary_new_tab": false, "secondary_button_label": "", "secondary_button_link": "", "secondary_button_style": "solid", "secondary_text_color": "#ffffff", "secondary_background_color": "#222222", "secondary_border_color": "#222222", "secondary_button_corner": 0, "secondary_new_tab": false}}}, "block_order": ["caption_3RACaT", "heading_jTDH3L", "text_BDJTzw", "buttons_zEq6pA"], "name": "❤️ Square: Wavy Banner & Button", "settings": {"content_alignment": "center", "content_layout_desktop": "vertical", "wave_color": "#e0f7fa", "enable_wave_animation": true, "section_width": 80, "section_max_width": "", "vertical_padding": 40, "vertical_margin": 0, "vertical_padding_mobile": 40, "vertical_margin_mobile": 20}}, "tsapb_e1c414da_FXXEbh": {"type": "tsapb-e1c414da", "blocks": {"heading_XhfDcT": {"type": "heading", "settings": {"title": "Best Sellers", "label": "", "description": "", "headingTextSize": "40", "headingAlignment": "left", "headingTextColor": "#222222", "show_view_all": true, "view_all_link": "shopify://collections", "view_all_label": "View All"}}, "products_GQVX4L": {"type": "products", "settings": {"product_list": ["baby", "col2", "shir3", "shirt", "shirt-1"]}}}, "block_order": ["heading_XhfDcT", "products_GQVX4L"], "custom_css": [".VtlsHeader__HeadingWrapper {display: flex; align-items: center; justify-content: space-between;}", ".VtlsHeader__HeadingWrapper a {text-decoration: underline;}"], "name": "❤️ Square: Featured Products Carousel", "settings": {"imageAspectRatio": "square", "productTextAlignment": "left", "productCardTextColor": "#222222", "productCardDiscountedPriceColor": "#ce1900", "productCardBackgroundColor": "", "productCardBorderColor": "#000000", "productCardRadius": 12, "showSecondImageOnHoverCheckbox": true, "showVendorCheckbox": false, "showProductRatingCheckbox": true, "showAddToCartCheckbox": true, "addToCartButtonStyle": "filled", "addToCartButtonTextColor": "#ffffff", "addToCartButtonColor": "#e1680b", "addToCartButtonCornerRadius": 4, "visibleItemsDesktop": "4", "visibleItemsMobile": "2", "productCardGapSize": 16, "sectionMaxWidth": 1200, "sectionMakeFullWidthCheckbox": true, "sectionPaddingTop": 48, "sectionPaddingBottom": 48, "sectionBackgroundColor": "", "arrowsAndThumbProgressBarColor": "#222222"}}, "otsb_scrolling_banner_2_jCTM49": {"type": "otsb-scrolling-banner-2", "blocks": {"text_paFRfn": {"type": "text", "settings": {"text": "<p>Flash Sale</p>", "use_custom_font": true, "text_custom_font": "serif", "text_size": 100, "text_color": "secondary", "color_link": "rgba(0,0,0,0)", "spacing_left": 0, "spacing_right": 0, "spacing_left_mobile": 0, "spacing_right_mobile": 0}}, "icon_KWYiqc": {"type": "icon", "settings": {"icon": "gift", "another_icon": "", "custom_icon": "", "height_icon": 25, "icon_color": "primary", "spacing_left": 0, "spacing_right": 0, "spacing_left_mobile": 0, "spacing_right_mobile": 0}}}, "block_order": ["text_paFRfn", "icon_KWYiqc"], "name": "OT: Scrolling Promo #2", "settings": {"show_border": true, "speed": 48, "direction": "right", "pause_hover": false, "bg_light": "#fff3cd", "color_text_light": "#ff0000", "color_text_light_2": "#000000", "color_line": "rgba(0,0,0,0)", "full_width": true, "content_max_width": 1200, "space_block": 45, "padding_top": 24, "padding_bottom": 24, "space_block_mobile": 45, "padding_top_mobile": 12, "padding_bottom_mobile": 12}}, "vso_countdown_header_ewnqN9": {"type": "vso-countdown-header", "name": "VSO - Countdown Header 01", "settings": {"text": "<p>Hurry! Flash Sale Ends In<br/></p>", "button": "", "button_url": "shopify://collections/all", "countdown_mode": "duration", "countdown_end_date": "2025-12-31", "countdown_end_time": "23:59:59", "countdown_duration_hours": 3, "countdown_duration_minutes": 27, "countdown_duration_seconds": 0, "label_days": "Days", "label_hours": "Hrs", "label_minutes": "<PERSON>s", "label_seconds": "Secs", "text_custom": true, "text_font": "montserrat_n4", "text_size": 24, "text_size_mobile": 18, "text_height": 120, "text_align": "center", "countdown_number_custom": true, "countdown_number_font": "montserrat_n4", "countdown_number_size": 38, "countdown_number_size_mobile": 32, "countdown_number_weight": 700, "countdown_gap": 20, "countdown_gap_mobile": 15, "countdown_labels_custom": false, "countdown_labels_font": "assistant_n4", "countdown_labels_size": 14, "countdown_labels_size_mobile": 12, "countdown_labels_weight": 400, "countdown_labels_mt": 4, "button_custom": false, "button_font": "assistant_n6", "button_size": 16, "button_size_mobile": 14, "button_weight": 600, "button_height": 100, "button_ml": 15, "button_padding_vertical": 12, "button_padding_horizontal": 24, "button_radius": 25, "button_border_thickness": 0, "text_color": "#e6f0ff", "countdown_number_color": "#ffffff", "countdown_labels_color": "#ffffff", "button_color": "#f8f8f8", "button_hover_color": "#132a45", "button_bg_color": "#e1680b", "button_bg_hover_color": "#3b82f6", "button_border_color": "#1b6fa8", "button_border_hover_color": "#155e9c", "background_color": "#100f0f", "background_gradient": "linear-gradient(135deg, rgba(10, 25, 47, 1), rgba(17, 94, 140,", "border_color": "#1e3a8a", "margin_top": 0, "margin_bottom": 0, "padding_top": 20, "padding_bottom": 20, "padding_horizontal": 2, "padding_horizontal_mobile": 1.5, "border_thickness": 0}}, "17571600843c76dd95": {"type": "apps", "blocks": {"layerup_offer_images_MxYq3Y": {"type": "shopify://apps/layerup/blocks/offer-images/7d11b4af-311f-446e-8436-800e18c7a588", "settings": {"bg": "#fff3cd", "collection": "new1", "banner_image": "shopify://shop_images/0e8e4284-fc4d-488a-aff7-66af873a497f-Photoroom_33d7341f-be9a-413d-b5a2-e73b6b3b15ef.png", "banner_link": "shopify://collections/new1", "heading": "", "heading_size": 24, "heading_color": "#000000", "button_bg": "#e1680b", "button_text_color": "#ffffff", "button_margin_top": 20, "padding_top": 36, "padding_bottom": 36}}}, "block_order": ["layerup_offer_images_MxYq3Y"], "settings": {"include_margins": true}}, "8b19d091-4504-41ad-93d0-14710651487b": {"type": "cartrek-product-tab", "blocks": {"340c7579-755b-4996-b27e-01d5e6748950": {"type": "tab", "settings": {"collection": "all-shoes", "title": "All Shoes"}}, "9ca84213-3400-40c7-8995-f412554efb65": {"type": "tab", "settings": {"collection": "loafer", "title": "Casuals"}}, "c1c93b8f-68e0-4054-b260-bd6d08536a0c": {"type": "tab", "settings": {"collection": "sports", "title": "Sports"}}, "558433ec-a823-4bc5-951d-a4a753d976df": {"type": "tab", "settings": {"collection": "sneakers", "title": "Sneakers"}}, "fba7db69-eb80-4a64-862b-3c14ded2ab49": {"type": "tab", "settings": {"collection": "boots", "title": "Boots"}}}, "block_order": ["340c7579-755b-4996-b27e-01d5e6748950", "9ca84213-3400-40c7-8995-f412554efb65", "c1c93b8f-68e0-4054-b260-bd6d08536a0c", "558433ec-a823-4bc5-951d-a4a753d976df", "fba7db69-eb80-4a64-862b-3c14ded2ab49"], "disabled": true, "settings": {"page_full_width": false, "page_full_width_spacing": false, "title": "SUGGESTED FOR YOU", "heading_size": "h1", "text": "", "column_alignment": "left", "button_label": "Button label", "button_link": "", "color_scheme": "background-1", "products_to_show": 12, "products_per_row": 4, "enable_slider": true, "enable_dotts": true, "enable_arrows": false, "enable_tab_title": true, "enable_tab_count": false, "enable_collection_icon": false, "show_view_all": false, "image_ratio": "square", "show_secondary_image": false, "add_image_padding": false, "show_vendor": false, "layout": "none", "image_block_heading": "Main Heading", "image_block_sub_heading": "Sub Heading", "image_block_description": "", "image_block_button_text": "View More", "image_block_button_link": "", "image_block_horizontal_position": "left", "image_block_vertical_position": "vertical_top", "padding_top": 0, "padding_bottom": 65, "custom_class_name": "suggested-product-tab"}}, "51aa2c05-65d4-4f7b-9530-c01347bcf6a0": {"type": "dt-collapsible-content", "blocks": {"template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-1": {"type": "collapsible_row", "settings": {"heading": "What sizes do you offer?", "icon": "check_mark", "row_content": "<p><PERSON>ug<PERSON>t sed lectus vestibulum mattis. Dui vivamus arcu felis bibendum. Aliquam faucibus  tempor. Massa eget egestas purus viverra accumsan in.</p>", "page": ""}}, "template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-2": {"type": "collapsible_row", "settings": {"heading": "What is your return policy?", "icon": "check_mark", "row_content": "<p>Facilisis leo vel fringilla est ullamcorper eget nulla facilisi etiam. Ultrices mi tempus imperdiet nulla malesuada. Vitae nunc sed velit dignissim.</p>", "page": ""}}, "template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-3": {"type": "collapsible_row", "settings": {"heading": "How can I track my order?", "icon": "check_mark", "row_content": "<p><PERSON><PERSON><PERSON> augue interdum velit euismod in. Adipiscing diam donec adipiscing tristique risus nec feugiat in. Ut placerat orci nulla dignissim enim.</p>", "page": ""}}, "template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-4": {"type": "collapsible_row", "settings": {"heading": "Are your shoes made from sustainable materials?", "icon": "check_mark", "row_content": "<p>Tristique risus nec feugiat in fermentum. Non odio euismod lacinia at quis risus. Leo pellentesque habitant morbi  duis ut diam quam nulla porttitor.</p>", "page": ""}}, "9cc39cce-2da5-4d87-8afa-55eb6d3fcaf0": {"type": "collapsible_row", "settings": {"heading": "How do I care for my shoes?", "icon": "check_mark", "row_content": "<p>Facilisis gravida neque convallis a cras. <PERSON><PERSON><PERSON> pharetra et ultrices neque ornare aenean euismod elementum nisi. Pulvinar tristique senectus et netus.</p>", "page": ""}}}, "block_order": ["template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-1", "template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-2", "template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-3", "template--21701471240498__51aa2c05-65d4-4f7b-9530-c01347bcf6a0-collapsible_row-4", "9cc39cce-2da5-4d87-8afa-55eb6d3fcaf0"], "disabled": true, "settings": {"title": "Frequently asked questions", "heading_size": "h0", "sub_heading": "", "description": "", "button_label": "", "button_link": "", "button_style_secondary": false, "column_alignment": "left", "layout": "none", "color_scheme": "background-1", "container_color_scheme": "accent-1", "open_first_collapsible_row": true, "enable_address_block": false, "video_url": "https://www.youtube.com/watch?v=_9VUPq3SxOc", "icon_1_text": "", "image_ratio": "large", "desktop_layout": "image_second", "padding_top": 88, "padding_bottom": 0, "custom_class_name": ""}}, "otsb_rich_text_2_L7qaJz": {"type": "otsb-rich-text-2", "blocks": {"heading_XH34TH": {"type": "heading", "settings": {"heading": "Story [About Us]", "highlight_type": "underline", "heading_size": 100, "heading_tag": "h2", "heading_text_transform": "uppercase"}}, "text_VBhMQr": {"type": "text", "settings": {"text": "<p><strong>More Than Gifts. Memories Made Personal.</strong><br/><br/>We believe the best gifts are personal. At Gifting Studio, every item is thoughtfully designed with care and love—made not just to give, but to cherish. Our products aren’t just gifts; they are keepsakes created to celebrate the moments that matter most.</p>", "read_more_label": "Read more", "see_less_label": "See less"}}, "button_cPbieM": {"type": "button", "settings": {"button_label": "View more", "button_link": "", "open_new_window_button": false, "show_button_style_1": "primary", "button_label_2": "", "open_new_window_button_2": false, "button_link_2": "", "show_button_style_2": "primary", "button_type": "rounded", "button_color_mobile": "color", "button_animation": "fill_up", "custom_icon_button": "", "color_button": "#ff5900", "color_button_hover": "#ff5900", "color_text_button": "#ffffff", "color_text_button_hover": "#ffffff", "color_button_secondary": "rgba(0,0,0,0)", "secondary_button_text": "#ff5900", "colors_text_link": "rgba(0,0,0,0)"}}}, "block_order": ["heading_XH34TH", "text_VBhMQr", "button_cPbieM"], "name": "OT: Rich text #2", "settings": {"heading": "#242424", "text": "#242424", "text_link": "rgba(0,0,0,0)", "color_heading_highlight": "#ff5900", "background_color": "#ffffff", "change_heading_font": false, "type_header_font": "assistant_n4", "content_alignment": "center", "show_section_divider": false, "make_full_page_width": true, "padding_top": 28, "padding_bottom": 28, "content_alignment_mobile": "center", "show_section_divider_mobile": false, "padding_top_mobile": 28, "padding_bottom_mobile": 28}}, "vso_testimonials_02_wUEQ3M": {"type": "vso-testimonials-02", "blocks": {"star_card_PtED77": {"type": "star_card", "settings": {"title": "Review Title ⭐", "description": "<em>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.</em>", "author": "<PERSON>", "verified_text": "Verified customer"}}, "star_card_nhKWNU": {"type": "star_card", "settings": {"title": "Review Title ⭐", "description": "<em>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.</em>", "author": "<PERSON>", "verified_text": "Verified customer"}}, "star_card_39pj6k": {"type": "star_card", "settings": {"title": "Review Title ⭐", "description": "<em>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.</em>", "author": "<PERSON>", "verified_text": "Verified customer"}}, "star_card_pUiwFB": {"type": "star_card", "settings": {"title": "Review Title ⭐", "description": "<em>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.</em>", "author": "<PERSON>", "verified_text": "Verified customer"}}, "star_card_gAPYCm": {"type": "star_card", "settings": {"title": "Review Title ⭐", "description": "<em>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.</em>", "author": "<PERSON>", "verified_text": "Verified customer"}}}, "block_order": ["star_card_PtED77", "star_card_nhKWNU", "star_card_39pj6k", "star_card_pUiwFB", "star_card_gAPYCm"], "name": "VSO - Testimonials 02", "settings": {"desktop_blocks_gap": 32, "mobile_blocks_gap": 16, "title": "Customer Reviews", "title_size_desktop": 40, "title_size_mobile": 32, "custom_title_font": true, "title_font": "baskervville_n4", "title_color": "#000000", "title_line_height": 130, "title_font_weight": 700, "description": "", "description_size_desktop": 20, "description_size_mobile": 18, "description_line_height": 150, "description_color": "#0b0b0b", "description_max_width": 600, "description_top_spacing": 15, "description_bottom_spacing": 0, "rating_text": "Excellent", "rating_value": 4.6, "rating_star_count": 4.5, "rating_text_size_desktop": 22, "rating_text_size_mobile": 20, "rating_text_line_height": 120, "rating_text_font_weight": 400, "rating_text_color": "#121212", "rating_number_color": "#121212", "rating_star_size_desktop": 32, "rating_star_size_mobile": 24, "rating_star_filled_color": "rgba(0,0,0,0)", "rating_star_empty_color": "rgba(0,0,0,0)", "rating_star_color": "#ffd700", "rating_container_gap": 8, "rating_container_top_spacing": 8, "rating_container_bottom_spacing": 0, "rating_container_bg_color": "#ffffff", "rating_container_padding_vertical": 12, "rating_container_padding_horizontal": 20, "rating_container_border_radius": 8, "rating_container_shadow_enable": true, "rating_container_shadow_color": "#000000", "star_size": 32, "star_color": "#ffd700", "star_background_color": "rgba(0,0,0,0)", "stars_title_gap": 8, "custom_block_title_font": false, "block_title_font": "sans_serif_n4", "block_title_size_desktop": 20, "block_title_size_mobile": 18, "block_title_line_height": 120, "block_title_font_weight": 600, "block_title_color": "#000000", "custom_block_description_font": false, "block_description_font": "sans_serif_n4", "block_description_size_desktop": 16, "block_description_size_mobile": 14, "block_description_line_height": 150, "block_description_font_weight": 400, "block_description_color": "#232323", "block_author_size_desktop": 14, "block_author_size_mobile": 13, "block_author_line_height": 140, "block_author_font_weight": 600, "block_author_color": "#333333", "block_verified_size_desktop": 12, "block_verified_size_mobile": 11, "block_verified_line_height": 130, "block_verified_font_weight": 400, "block_verified_color": "#888888", "author_circle_size": 40, "author_circle_size_mobile": 36, "author_circle_text_size": 22, "author_circle_text_size_mobile": 20, "author_circle_background_color": "#05b67c", "author_circle_text_color": "#ffffff", "author_container_gap": 8, "description_line_width": 2, "description_line_color": "#e1680b", "description_line_spacing": 12, "block_background_color": "#ffffff", "block_padding": 24, "block_elements_gap": 16, "block_border_radius": 30, "block_border_width": 0, "block_border_color": "#e0e0e0", "enable_card_shadow": true, "card_shadow_color": "#696969", "enable_equal_height": false, "desktop_card_width": 350, "header_blocks_gap": 0, "enable_fade_effect": true, "enable_fade_effect_mobile": false, "show_navigation_desktop": true, "show_navigation_mobile": true, "nav_arrow_size": 50, "nav_arrow_gap": 20, "nav_arrow_top_spacing": 0, "nav_arrow_bg_color": "#ffffff", "nav_arrow_icon_color": "#6b7280", "nav_arrow_border_color": "#e5e7eb", "nav_arrow_border_width": 0, "nav_arrow_shadow_enable": true, "nav_arrow_shadow_color": "#000000", "background_style": "gradient", "background_color": "#f7f7f7", "gradient_start_color": "rgba(0,0,0,0)", "gradient_end_color": "rgba(0,0,0,0)", "padding_top_desktop": 60, "padding_bottom_desktop": 60, "margin_top_desktop": 0, "margin_bottom_desktop": 0, "padding_top_mobile": 32, "padding_bottom_mobile": 32, "margin_top_mobile": 0, "margin_bottom_mobile": 0}}, "vso_icon_benefits_01_AKCCjx": {"type": "vso-icon-benefits-01", "blocks": {"benefit_CcXrCD": {"type": "benefit", "settings": {"icon": "shopify://shop_images/14-Password-ezgif.com-video-to-gif-converter.webp", "title": "High Quality Material", "text": ""}}, "benefit_Nkxct3": {"type": "benefit", "settings": {"icon": "shopify://shop_images/14Deliverytruck-ezgif.com-video-to-gif-converter.webp", "title": "Fast & Secure", "text": ""}}, "benefit_rgTGJc": {"type": "benefit", "settings": {"icon": "shopify://shop_images/cake-ezgif.com-video-to-gif-converter.gif", "title": "Personalization Available", "text": ""}}, "benefit_nCiKVV": {"type": "benefit", "settings": {"icon": "shopify://shop_images/04Delivery-ezgif.com-video-to-gif-converter.gif", "title": "3 Days Easy Replacement", "text": ""}}}, "block_order": ["benefit_CcXrCD", "benefit_Nkxct3", "benefit_rgTGJc", "benefit_nCiKVV"], "name": "VSO - Icon Benefits 01", "settings": {"title": "Why Choose _Us ?_", "title_size_desktop": 40, "title_size_mobile": 32, "custom_title_font": true, "title_font": "andada_pro_n4", "title_color": "#000000", "title_highlight_color": "#e1680b", "title_line_height": 130, "subtitle": "", "subtitle_size_desktop": 20, "subtitle_size_mobile": 16, "custom_subtitle_font": false, "subtitle_font": "sans_serif_n4", "subtitle_color": "#000000", "subtitle_line_height": 130, "header_elements_gap": 16, "header_blocks_gap": 50, "custom_blocks_title_font": false, "blocks_title_font": "sans_serif_n4", "block_title_size_desktop": 18, "block_title_size_mobile": 18, "block_title_line_height": 120, "custom_blocks_text_font": false, "blocks_text_font": "sans_serif_n4", "block_text_size_desktop": 16, "block_text_size_mobile": 14, "block_text_line_height": 150, "blocks_title_color": "#000000", "blocks_text_color": "#000000", "block_background_color": "rgba(0,0,0,0)", "desktop_icon_size": 100, "block_padding": 0, "block_elements_gap": 16, "block_border_radius": 8, "mobile_layout": "column", "mobile_blocks_gap": 16, "mobile_peek_percentage": 5, "background_color": "rgba(0,0,0,0)", "content_alignment": "center", "blocks_alignment": "center", "desktop_blocks_gap": 32, "section_max_width": 64, "full_width": true, "padding_top_desktop": 60, "padding_bottom_desktop": 60, "margin_top_desktop": 0, "margin_bottom_desktop": 0, "padding_top_mobile": 32, "padding_bottom_mobile": 32, "margin_top_mobile": 0, "margin_bottom_mobile": 0, "padding_horizontal_mobile": 15}}, "17571756210f1912c4": {"type": "apps", "settings": {"include_margins": true}}, "fc6b2837-c805-4fa1-8713-abdc1fdc3cde": {"type": "dt-instagram-gallery", "blocks": {"52948baa-63a4-481e-85a5-7de874e48ac2": {"type": "text", "settings": {"show_content": true, "block_sub_title": "", "block_description": "", "block_button_link": "https://www.instagram.com/", "desktop_content_alignment": "center"}}, "b732a2a2-6374-4c67-9bab-eaa752ec39b8": {"type": "text", "settings": {"show_content": true, "block_sub_title": "", "block_description": "", "block_button_link": "https://www.instagram.com/", "desktop_content_alignment": "center"}}, "2f8428eb-8574-42bb-8ba8-fe5ccd9b5f9b": {"type": "text", "settings": {"show_content": true, "block_sub_title": "", "block_description": "", "block_button_link": "https://www.instagram.com/", "desktop_content_alignment": "center"}}, "ff29113d-445c-46e3-93e0-b462622a32c9": {"type": "text", "settings": {"show_content": true, "block_sub_title": "", "block_description": "", "block_button_link": "https://www.instagram.com/", "desktop_content_alignment": "center"}}, "1492d681-7703-4b85-aa58-2d00566fa942": {"type": "text", "settings": {"show_content": true, "block_sub_title": "", "block_description": "", "block_button_link": "https://www.instagram.com/", "desktop_content_alignment": "center"}}, "e67b99a1-00a8-433d-bf4c-d8b99f5980e8": {"type": "text", "settings": {"show_content": true, "block_sub_title": "", "block_description": "", "block_button_link": "https://www.instagram.com/", "desktop_content_alignment": "center"}}, "108061e4-24d2-4133-8e56-f7f04c29c50e": {"type": "text", "settings": {"show_content": true, "block_sub_title": "", "block_description": "", "block_button_link": "https://www.instagram.com/", "desktop_content_alignment": "center"}}}, "block_order": ["52948baa-63a4-481e-85a5-7de874e48ac2", "b732a2a2-6374-4c67-9bab-eaa752ec39b8", "2f8428eb-8574-42bb-8ba8-fe5ccd9b5f9b", "ff29113d-445c-46e3-93e0-b462622a32c9", "1492d681-7703-4b85-aa58-2d00566fa942", "e67b99a1-00a8-433d-bf4c-d8b99f5980e8", "108061e4-24d2-4133-8e56-f7f04c29c50e"], "custom_css": [], "settings": {"page_full_width": true, "page_full_width_spacing": false, "title": "", "heading_size": "h1", "sub_heading": "", "description": "", "column_alignment": "center", "button_label": "Button label", "button_link": "", "color_scheme": "background-1", "padding_top": 0, "padding_bottom": 0, "swiper_enable": true, "desktop_column": 7, "laptop_column": 4, "tablet_column": 2, "mobile_column": 1, "centered_slide": false, "swiper_pagination": false, "swiper_navigation": false, "auto_play": 0, "swipe_on_mobile": false, "arrow_on_mobile": false, "columns_mobile": "1", "custom_class_name": "instagram-demo"}}, "175715495227575b54": {"type": "apps", "settings": {"include_margins": true}}}, "order": ["otsb_circle_menu_2_RNkPHm", "otsb_slideshow_4_QEUGY9", "tsapb_ca577056_CdhLLr", "c81d40c6-6c76-4fd0-9717-f3423af6c9c2", "1757166247f04c902a", "otsb_featured_collection_4_3N3rtP", "custom_liquid_Mrr7Dt", "tsapb_c3ee5db1_VN7HtM", "tsapb_e1c414da_FXXEbh", "otsb_scrolling_banner_2_jCTM49", "vso_countdown_header_ewnqN9", "17571600843c76dd95", "8b19d091-4504-41ad-93d0-14710651487b", "51aa2c05-65d4-4f7b-9530-c01347bcf6a0", "otsb_rich_text_2_L7qaJz", "vso_testimonials_02_wUEQ3M", "vso_icon_benefits_01_AKCCjx", "17571756210f1912c4", "fc6b2837-c805-4fa1-8713-abdc1fdc3cde", "175715495227575b54"]}