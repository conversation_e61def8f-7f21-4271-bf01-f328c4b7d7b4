{%- liquid
    if settings.heading_base_size != blank
    assign heading_size = settings.heading_base_size | times: section.settings.heading_size | times: 0.000225
    else
    assign heading_size = section.settings.heading_size | times: 100 | times: 0.0004
    endif
    if settings.heading_scale
    assign heading_size = heading_size | times: settings.heading_scale | times: 0.01 
    endif

    assign columns_mobile = section.settings.columns_mobile | plus: 0
    assign columns_desktop = section.settings.columns_desktop | plus: 0
    assign swiper_on_mobile = section.settings.swiper_on_mobile
    if section.blocks.size <= columns_mobile
        assign swiper_on_mobile = false
    endif

    assign enable_desktop_slider = section.settings.enable_desktop_slider
    if section.blocks.size <= columns_desktop
        assign enable_desktop_slider = false
    endif
    
    assign ratio = 100
    if section.settings.image_ratio == 'rectangle'
        assign ratio = 150
    endif
    if section.settings.image_ratio == 'landscape'
        assign ratio = 75
    endif
    if section.settings.image_ratio == '3/4'
        assign ratio = 133
    endif
    if settings.lang_direction contains request.locale.iso_code
        assign is_rtl = true
        assign class_rtl = 'margin-left'
    else
        assign is_rtl = false
        assign class_rtl = 'margin-right'
    endif
-%}

{%- style -%}
    .otsb__root .heading--{{ section.id }} {
        font-size: {{ heading_size | times: 0.6 }}rem;
    }
    .gap-mobile-{{ section.id }} {
    gap: {{ section.settings.spacing_mobile }}px;
    }

    #shopify-section-{{ section.id }}{
    {% if section.settings.color_text_link.alpha != 0 %}
        --colors-text-link: {{ section.settings.color_text_link.red }}, {{ section.settings.color_text_link.green }}, {{ section.settings.color_text_link.blue }};
    {% else %}
        --colors-text-link: var(--color-foreground);
    {% endif %}
    }

    #shopify-section-{{ section.id }}{
    background: {{ section.settings.background_color_light }};
    {% if section.settings.color_heading.alpha != 0.0 %}
        --colors-heading: {{ section.settings.color_heading.red }},{{ section.settings.color_heading.green }},{{ section.settings.color_heading.blue }};
        --colors-heading-base: {{ section.settings.color_heading.red }},{{ section.settings.color_heading.green }},{{ section.settings.color_heading.blue }};
    {% else %}
       --colors-heading-base: var(--color-foreground);
    {% endif %}
    {%- unless section.settings.color_line.alpha == 0.0 -%}
        --colors-line-and-border: {{ section.settings.color_line.red }}, {{ section.settings.color_line.green }}, {{ section.settings.color_line.blue }};
        --colors-line-and-border-base: {{ section.settings.color_line.red }}, {{ section.settings.color_line.green }}, {{ section.settings.color_line.blue }};
    {%- endunless -%}
    }
    .content--{{ section.id }} {
    {% if section.settings.text_light.alpha != 0.0 %}
        --colors-text: {{ section.settings.text_light.red }},{{ section.settings.text_light.green }},{{ section.settings.text_light.blue }};
    {% endif %}

    {% if section.settings.title_color.alpha != 0.0 %}
        --colors-heading: {{ section.settings.title_color.red }},{{ section.settings.title_color.green }},{{ section.settings.title_color.blue }};
    {% endif %}
    }
    .dark .content--{{ section.id }} {
    {% if section.settings.title_color_dark.alpha != 0.0 %}
        --colors-heading: {{ section.settings.title_color_dark.red }},{{ section.settings.title_color_dark.green }},{{ section.settings.title_color_dark.blue }};
    {% else %}
        {% if settings.colors_dark_heading.red != blank and settings.colors_dark_heading.green != blank and settings.colors_dark_heading.blue != blank %}
            --colors-heading: {{ settings.colors_dark_heading.red }}, {{ settings.colors_dark_heading.green }}, {{ settings.colors_dark_heading.blue }};
        {% else %}
            --color-heading: 255, 255, 255;
        {% endif %}
    {% endif %}
    {% if section.settings.text_dark.alpha != 0.0 %}
        --colors-text: {{ section.settings.text_dark.red }},{{ section.settings.text_dark.green }},{{ section.settings.text_dark.blue }};
    {% else %}
        {% if settings.colors_dark_text.red != blank and settings.colors_dark_text.green != blank and settings.colors_dark_text.blue != blank %}
            --colors-heading: {{ settings.colors_dark_text.red }}, {{ settings.colors_dark_text.green }}, {{ settings.colors_dark_text.blue }};
        {% else %}
            --color-text: 153, 153, 153;
        {% endif %}
        --colors-text: {{ settings.colors_dark_text.red }}, {{ settings.colors_dark_text.green }}, {{ settings.colors_dark_text.blue }};
    {% endif %}
    }
    .dark #shopify-section-{{ section.id }}{
    {% if section.settings.background_color_dark == blank %}
        background: transparent;
    {% else %}
        background: {{ section.settings.background_color_dark }};
    {% endif %}
    }
    #shopify-section-{{ section.id }} .otsb-page-width {
        padding: 0 1.5rem;
    }
    #shopify-section-{{ section.id }} .otsb-page-width.otsb-carousel-mobile {
        padding: 0;
    }
    @media screen and (min-width: 768px) {
    #shopify-section-{{ section.id }} .otsb-page-width,
    #shopify-section-{{ section.id }} .otsb-page-width.otsb-carousel-mobile {
        padding: 0 50px;
    }
    .gap-{{ section.id }} {
    {% if enable_desktop_slider %}
        gap: 0px;
    {% else %}
        gap: {{ section.settings.spacing }}px;
    {% endif %}
    }
    .otsb__root .heading--{{ section.id }}{ font-size: {{ heading_size }}rem; }
    }
    {% if enable_desktop_slider %}
        @media (min-width: 1024px){
        .otsb__root .splide__slide.preload-slide--{{ section.id }} {
        {{ class_rtl }}: {{ section.settings.spacing }}px;
        width: calc(((100% + {{ section.settings.spacing }}px) / {{ columns_desktop }}) - {{ section.settings.spacing }}px);
        }
        }
    {% endif %}
    {% if swiper_on_mobile %}
        @media (max-width: 1023px) {
        .otsb__root .splide__slide.preload-slide--{{ section.id }} {
        {{ class_rtl }}: {{ section.settings.spacing_mobile }}px;
        width: calc(((100% + {{ section.settings.spacing_mobile }}px) / 2) - {{ section.settings.spacing_mobile }}px);
        }
        }
        @media (max-width: 767px) {
        .otsb__root .splide__slide.preload-slide--{{ section.id }} {
        {{ class_rtl }}: {{ section.settings.spacing_mobile }}px;
        {% if section.settings.enable_mobile_nav == false %}
          {% if columns_mobile == 1 %}
          width: calc(((70% + {{ section.settings.spacing_mobile }}px) / {{ columns_mobile }}) - {{ section.settings.spacing_mobile }}px);
          {% else %}
           width: calc(((80% + {{ section.settings.spacing_mobile }}px) / {{ columns_mobile }}) - {{ section.settings.spacing_mobile }}px); 
          {% endif %}
        {% else %}
          width: calc(((100% + {{ section.settings.spacing_mobile }}px) / {{ columns_mobile }}) - {{ section.settings.spacing_mobile }}px);
        {% endif %}
        }
        {% if section.settings.enable_mobile_nav == false %}
        #shopify-section-{{ section.id }} .splide__track {
          padding-left: 15px!important;
          padding-right: 15px!important;
        }
        {% endif %}
        }
    {% endif %}
    #shopify-section-{{ section.id }} button.otsb-button-arrow {
    background-color: rgba({{ section.settings.slider_button_color.red }}, {{ section.settings.slider_button_color.green }}, {{ section.settings.slider_button_color.blue }}, 0.3);
    color: {{ section.settings.slider_button_color }};
    box-shadow: none;
    border-radius: 50px;
    }

    #shopify-section-{{ section.id }} button.otsb-button-arrow:hover {
    color: {{ section.settings.slider_button_hover_text_color }};
    background: {{ section.settings.slider_button_hover_color }};
    }
{%- endstyle -%}
{% if request.design_mode %}
    <style>
  .otsb_nope {
    display: none !important;
    height: 0 !important;
    overflow: hidden !important;
    visibility: hidden !important;
    width: 0 !important;
    opacity: 0 !important;
  }
</style>
  <style>
    ._otsb_warning {
      position: relative;
      box-shadow: 0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07);
      border-radius: 1rem;
    }
    ._otsb_warning::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      box-shadow: 0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, 0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset;
      border-radius: 1rem;
      pointer-events: none;
      mix-blend-mode: luminosity;
    }
  </style>
  <div x-data="otsb_script_require" class="page-width" style="margin-top:36px;margin-bottom:36px">
    <div class="_otsb_warning">
      <div style="border-top-left-radius:1rem;border-top-right-radius:1rem;border:1px solid #fcaf0a;background:#fcb827;padding:1rem">
        <div style="align-items:center;gap:8px;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between">
          <div style="display:flex;gap:4px;flex-direction:row;flex-wrap:nowrap;justify-content:space-between">
            <span style="display:block;height:20px;width:20px;max-height:100%;max-width:100%;margin:auto">
              <svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"></path><path d="M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path><path fill-rule="evenodd" d="M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"></path></svg>
            </span>
            <h2 style="overflow-wrap:anywhere;word-break:normal;font-size:100%;font-weight:650;line-height:1.25;color:rgb(37,26,0)">App Embeds Are Disabled</h2>
          </div>
        </div>
      </div>
      <div style="padding:1rem;color:rgb(37,26,0)">
        <p>To use this section, the app embeds of OT: Theme Sections must be enabled in the theme editor. Please follow these steps to activate them:</p>
        <ul>
          <li>In the left panel, click the last icon that says <b>“App embeds”</b>.</li>
          <li>Enter <b>“OT”</b> on the search bar to quickly find and embed the apps from OT: Theme Sections.</li>
          <li>Turn on the Toggle buttons of "Section Builder Script" and "Section Builder Style", then click <b>Save</b>.</li>
        </ul>
        <p>Please refer to the User Guide <a href="https://support.omnithemes.com/blogs/ot-theme-sections-get-started/1-embed-app-to-shopify-theme" target="_blank">here</a></p>
        <p>For further support: feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>!</p>
      </div>
    </div>
  </div>
{% endif %}
<style>#shopify-section-{{section.id}} .otsb_trademark_root {user-select:none;color:#999;font-size:.75em;text-align:right;margin-top:2.5rem;}#shopify-section-{{section.id}} .otsb_trademark_root a {color:#999;background:none;text-decoration: none;}</style>
<div class="otsb__root otsb_nope" x-data="otsb_script_1">
    {% render 'otsb-section-divider' %}
    <div
        class="pt-[{{ section.settings.padding_top_mobile }}px] md:pt-[{{ section.settings.padding_top }}px] pb-[{{ section.settings.padding_bottom_mobile }}px] md:pb-[{{ section.settings.padding_bottom }}px]">
        {%- unless section.settings.heading == blank -%}
            <div
            class="text-center mb-5{% unless section.settings.heading_alignment contains 'center' %} flex items-center{% endunless %} otsb-heading-{{ section.settings.heading_alignment }} pr-6 pl-6{% unless section.settings.full_width %} otsb-page-width{% endunless %}">
            <{{ section.settings.heading_tag }} class="heading--{{ section.id }} otsb-section-header otsb-h2 block
            leading-tight flex-auto otsb-p-break-words">
            {{- section.settings.heading | escape -}}
        </{{ section.settings.heading_tag }}>
        {%- if section.settings.show_view_all -%}
            <div class="flex justify-center pt-0.5 pb-0.5 md:pb-2.5 md:pt-2.5">
                <a
                    id="ViewAll-{{ section.id }}"
                    class="otsb-nav-link otsb-effect-inline relative flex items-center otsb-hover-text-link empty:otsb-hidden cursor-pointer"
                    href="{{ routes.collections_url }}"
                    aria-labelledby="ViewAll-{{ section.id }} SectionHeading-{{ section.id }}"
                >
                    View all<span class="w-2 h-2 inline-block ml-1.5 mt-0.5">
              {%- render 'otsb-icon-alls', icon: 'icon-double-arrow' -%}
            </span>
                </a>
            </div>
        {%- endif -%}
    </div>
    {%- endunless -%}
    <div
        class="mx-auto{% if swiper_on_mobile %} otsb-carousel-mobile otsb-carousel-tablet otsb-full-width-mobile{% endif %}{% if section.settings.full_width %} otsb-full-width{% else %} otsb-page-width {% endif %} ">
        <div class="otsb-collection_list otsb-content-wrapper  {% if section.settings.enable_mobile_nav == true %} pr-6 pl-6 {% endif %} {% if enable_desktop_slider %}lg:pl-24 lg:pr-24{% else %} lg:pl-0 lg:pr-0{% endif %}">
            <div
                x-data
                id="x-collection-{{ section.id }}-{{ block.id }}"
                {% if enable_desktop_slider or swiper_on_mobile -%}
                    class="{% if settings.lang_direction contains request.locale.iso_code and section.settings.transition_style == "fade" %}otsb-ltr {% endif %}x-splide splide visible"
                    x-intersect.once.margin.200px='
            $store.otsb_xSplide.load($el, {
              "speed": 1000,
              "pagination": false,
              "mediaQuery": "min",
              "updateOnMove": "true",
              {%- if settings.lang_direction contains request.locale.iso_code %}
                "direction": "rtl",
              {%- endif %}
              "progressBar": {{ section.blocks.size }},
              "breakpoints": {
                300: {
                  {% if swiper_on_mobile == false -%}
                    "destroy": true,
                  {%- endif %}
                  "gap": "{{ section.settings.spacing_mobile }}px",
                  "focus": "right",
                  "perPage": {{ columns_mobile }},
                  "perMove": {{ columns_mobile }},
                  "autoWidth": true
                },
                768: {
                  "gap": "{{ section.settings.spacing }}px",
                  
                  "perPage": 2,
                  "perMove": 2
                },
                1024: {
                  {% if enable_desktop_slider == false -%}
                    "destroy": true,
                  {%- else %}
                    "destroy": false,
                  {%- endif %}
                  "perPage": {{ columns_desktop }},
                  "perMove": {{ columns_desktop }},
                  
                }
              },
              "classes": {
                "arrows" : "block",
              }
            })
          '
                {%- endif -%}
            >
                <div
                    class="splide__track lg:pt-0.5 lg:pb-0.5{% if enable_desktop_slider %} md:cursor-grab{% endif %}{% if swiper_on_mobile %} {% if section.settings.enable_mobile_nav == false %}pl-6 pr-6 lg:pl-0 lg:pr-0{% endif %}{% endif %}">
                    <div
                        class="
              {%- if enable_desktop_slider == false -%}lg:otsb-grid lg:grid-cols-{{ columns_desktop }}{% else %}lg:flex lg:w-full lg:gap-0{% endif %} gap-{{ section.id }} splide__list
              {% if swiper_on_mobile %} flex w-full{% else %} otsb-grid grid-cols-{{ columns_mobile }} md:otsb-grid md:grid-cols-4 gap-mobile-{{ section.id }}{%- if enable_desktop_slider %} lg:grid-cols-none lg:flex{% endif %}{% endif %}
            "
                    >
                        {%- for block in section.blocks -%}
                            <div
                                x-slide-index="{{ forloop.index | minus: 1 }}"
                                id="Slide-{{ section.id }}-{{ forloop.index }}"
                                class="{% if swiper_on_mobile or enable_desktop_slider %} splide__slide x-splide-slide preload-slide--{{ section.id }}{% endif %}"
                                {{ block.shopify_attributes }}
                            >
                                {% case block.type %}
                                    {%- when 'featured_collection' -%}
                                        {% render 'otsb-card-collection',
                                            card_collection: block.settings.collection,
                                            custom_image_collection: block.settings.image_custom_collection,
                                            custom_name_collection: block.settings.custom_name_collection,
                                            image_ratio: section.settings.image_ratio,
                                            ratio: ratio,
                                            columns_desktop: columns_desktop,
                                            columns_mobile: columns_mobile,
                                            block_type: block.type,
                                            block: block,
                                            enable_desktop_slider: enable_desktop_slider,
                                            text_overlay: section.settings.show_text_overlay,
                                            title_color: section.settings.title_color,
                                            title_size: section.settings.title_size,
                                            content_alignment: section.settings.content_alignment,
                                            content_position: section.settings.content_position,
                                            image_overlay_opacity: section.settings.image_overlay_opacity,
                                            index: forloop.index,
                                            image_overlay_color: section.settings.image_overlay_color,
                                            edges_type: section.settings.edges_type,
                                            is_lazy: section.settings.is_lazyload
                                        %}
                                    {%- when 'promotion_block' -%}
                                        {% render 'otsb-promotion-block',
                                            image_ratio: section.settings.image_ratio,
                                            ratio: ratio,
                                            image: block.settings.image,
                                            button_label: block.settings.button_label,
                                            button_link: block.settings.button_link,
                                            alignment: block.settings.alignment,
                                            content_position: block.settings.content_position,
                                            heading: block.settings.heading,
                                            content: block.settings.content,
                                            columns_desktop: columns_desktop,
                                            columns_mobile: columns_mobile,
                                            block: block,
                                            enable_desktop_slider: enable_desktop_slider,
                                            open_new_window: block.settings.open_new_window,
                                            image_overlay_opacity: block.settings.image_overlay_opacity,
                                            title_color: section.settings.title_color,
                                            title_size: block.settings.title_size,
                                            text_size: block.settings.text_size,
                                            show_button_primary: block.settings.show_button_primary,
                                            color_button: block.settings.color_button,
                                            color_text_button: block.settings.color_text_button,
                                            color_text_link: section.settings.color_text_link,
                                            color_button_hover: block.settings.color_button_hover,
                                            color_text_button_hover: block.settings.color_text_button_hover,
                                            color_button_secondary: block.settings.color_button_secondary,
                                            secondary_button_text: block.settings.secondary_button_text,
                                            icon: block.settings.icon,
                                            custom_icon: block.settings.custom_icon,
                                            another_icon_1: block.settings.another_icon,
                                            icon_size: block.settings.icon_size,
                                            subheading: block.settings.subheading,
                                            image_overlay_color: section.settings.image_overlay_color,
                                            button_type: block.settings.button_type,
                                            button_color_mobile: block.settings.button_color_mobile,
                                            button_animation: block.settings.button_animation,
                                            custom_icon_button: block.settings.custom_icon_button,
                                            edges_type: section.settings.edges_type,
                                            is_lazy: section.settings.is_lazyload
                                        %}
                                {%- endcase -%}
                            </div>
                        {%- endfor -%}
                    </div>
                </div>
                {%- if enable_desktop_slider or section.settings.swiper_on_mobile == true  -%}
                    <div class="splide__arrows {% if enable_desktop_slider and section.settings.swiper_on_mobile == false  %} otsb-hidden md:block {% elsif enable_desktop_slider == false and section.settings.swiper_on_mobile == true %} {% if section.settings.enable_mobile_nav == true %}block{% else %} otsb-hidden {% endif %} md:otsb-hidden {% endif %} {% if enable_desktop_slider and section.settings.swiper_on_mobile == true  %}{% if section.settings.enable_mobile_nav == true %}block{% else %} otsb-hidden {% endif %}md:block{% endif %}">
                        
                    <button
                            class="left-0 md:left-[20px] md:left-0 flex md:block justify-center items-center rotate-90 splide__arrow splide__arrow--prev otsb-button-arrow lg:block absolute none_border z-2 w-14 h-14 lg:w-14 lg:h-14 rounded-full after:text-[20px] pt-2 pb-2 pl-2 pr-2 lg:pt-4 lg:pl-4 lg:pb-4 lg:pr-4 top-1/2 -translate-y-1/2 duration-100 opacity-60 hover:opacity-100 disabled:opacity-40 disabled:cursor-not-allowed{% if settings.lang_direction contains request.locale.iso_code %} right-0 lg:-right-24{% else %}  -rotate-180 left-0 lg:-left-24{% endif %}"
                            aria-label="previous slide"
                        >
                        <span class="w-4 h-4">
                            {% render 'otsb-icon-alls', icon: 'icon-caret'%}
                        </span>
                        
                        </button>
                        <button
                            class="right-0 md:right-[20px]  flex md:block justify-center items-center -rotate-90  splide__arrow splide__arrow--next otsb-button-arrow lg:block absolute none_border z-2 w-14 h-14 lg:w-14 lg:h-14 rounded-full after:text-[20px] pt-2 pb-2 pl-2 pr-2 lg:pt-4 lg:pl-4 lg:pb-4 lg:pr-4 top-1/2 -translate-y-1/2 duration-100 opacity-60 hover:opacity-100 disabled:opacity-40 disabled:cursor-not-allowed{% if settings.lang_direction contains request.locale.iso_code %} -rotate-180 left-0 lg:-left-24{% else %} right-0 lg:-right-24{% endif %}"
                            aria-label="next slide"
                        >
                        <span class="w-4 h-4">
                            {% render 'otsb-icon-alls', icon: 'icon-caret'%}  
                        </span>
                        </button>
                    </div>
                {%- endif -%}
                {%- if swiper_on_mobile or enable_desktop_slider -%}
                <div class="x-page-width page-width{% if enable_desktop_slider %} lg:flex{% else %} lg:hidden{% endif %}{% if swiper_on_mobile %} flex{% else %} hidden{% endif %} items-center gap-5 mt-2.5 w-full ml-auto mr-auto">
                  <div class="splide-progress inline-block grow rounded-md">
                    {% liquid
                      assign my_float = columns_desktop | times: 1.0
                      assign width_bar = my_float | divided_by: section.blocks.size 
                      assign mobile_float = columns_mobile | times: 1.0
                      assign mobile_width_bar = mobile_float | divided_by: section.blocks.size
                    %} 
                    {%- style -%}
                      #shopify-section-{{ section.id }} .splide-progress-bar-new.splide-progress-bar-{{ section.id }}-{{ block.id }} {
                        display: none;
                      }
                      #shopify-section-{{ section.id }} .splide-progress-bar-new {
                        background: rgba(var(--colors-progress-bar,0,0,0),1);
                        height: 3px;
                        transition: 400ms;
                        width: 0;
                      }
                      #shopify-section-{{ section.id }} .splide-progress {
                        overflow: hidden;
                      }
                      @media (min-width: 768px) {
                        #shopify-section-{{ section.id }} .splide-progress-bar-new.splide-progress-bar-{{ section.id }}-{{ block.id }} {
                            width: {{ width_bar | times: 100 }}%;
                            display: block;
                        }
                     }
                    {%- endstyle -%}
                    <div class="splide-progress-bar-{{ section.id }}-{{ block.id }} splide-progress-bar-new rounded-md"></div>
                  </div>
                </div>
              {%- endif -%}
            </div>
        </div>
    </div>
</div>
</div>